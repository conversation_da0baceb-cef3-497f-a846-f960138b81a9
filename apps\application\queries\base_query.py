"""
Query Pattern Implementation for CQRS
Defines base classes for queries and query handlers following CQRS pattern.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, TypeVar
from dataclasses import dataclass
from datetime import datetime, date


TQuery = TypeVar('TQuery')
TResult = TypeVar('TResult')


@dataclass
class QueryResult:
    """Standard query execution result."""
    success: bool
    data: Any = None
    total_count: int = 0
    message: str = "Query executed successfully"
    
    @classmethod
    def success_result(cls, data: Any = None, total_count: int = 0, message: str = "Query executed successfully"):
        """Create success result."""
        return cls(success=True, data=data, total_count=total_count, message=message)
    
    @classmethod
    def empty_result(cls, message: str = "No data found"):
        """Create empty result."""
        return cls(success=True, data=[], total_count=0, message=message)


@dataclass
class PaginationParams:
    """Pagination parameters for queries."""
    page: int = 1
    page_size: int = 20
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.page_size


@dataclass
class SortParams:
    """Sorting parameters for queries."""
    field: str
    direction: str = 'asc'  # 'asc' or 'desc'


class IQuery(ABC):
    """Base interface for all queries."""
    
    def __init__(self):
        self.query_id = datetime.now().isoformat()
        self.executed_at = None


class IQueryHandler(ABC, Generic[TQuery, TResult]):
    """Base interface for query handlers."""
    
    @abstractmethod
    async def handle(self, query: TQuery) -> TResult:
        """Handle the query and return result."""
        pass


# Patient Queries
@dataclass
class GetPatientByIdQuery(IQuery):
    """Query to get patient by ID."""
    patient_id: int


@dataclass
class SearchPatientsQuery(IQuery):
    """Query to search patients with filters."""
    search_term: str = ""
    cmnd: str = ""
    phone: str = ""
    email: str = ""
    pagination: PaginationParams = None
    sort: SortParams = None
    
    def __post_init__(self):
        if self.pagination is None:
            self.pagination = PaginationParams()
        if self.sort is None:
            self.sort = SortParams('NgayDangKy', 'desc')


@dataclass
class GetPatientMedicalHistoryQuery(IQuery):
    """Query to get patient's medical history."""
    patient_id: int
    date_from: Optional[date] = None
    date_to: Optional[date] = None


# Appointment Queries
@dataclass
class GetAppointmentByIdQuery(IQuery):
    """Query to get appointment by ID."""
    appointment_id: int


@dataclass
class GetAppointmentsByPatientQuery(IQuery):
    """Query to get appointments by patient."""
    patient_id: int
    status: Optional[str] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    pagination: PaginationParams = None


@dataclass
class GetAppointmentsByDoctorQuery(IQuery):
    """Query to get appointments by doctor."""
    doctor_id: int
    date: Optional[date] = None
    status: Optional[str] = None


@dataclass
class GetDoctorScheduleQuery(IQuery):
    """Query to get doctor's schedule."""
    doctor_id: int
    date_from: date
    date_to: date


@dataclass
class CheckAppointmentConflictQuery(IQuery):
    """Query to check appointment conflicts."""
    doctor_id: int
    appointment_date: date
    appointment_time: str
    exclude_appointment_id: Optional[int] = None


# Medical Staff Queries
@dataclass
class GetMedicalStaffByIdQuery(IQuery):
    """Query to get medical staff by ID."""
    staff_id: int


@dataclass
class GetAvailableDoctorsQuery(IQuery):
    """Query to get available doctors."""
    department_id: Optional[int] = None
    specialty: Optional[str] = None
    date_time: Optional[datetime] = None


@dataclass
class SearchMedicalStaffQuery(IQuery):
    """Query to search medical staff."""
    search_term: str = ""
    staff_type: Optional[str] = None
    department_id: Optional[int] = None
    pagination: PaginationParams = None


# Medication Queries
@dataclass
class GetMedicationByIdQuery(IQuery):
    """Query to get medication by ID."""
    medication_id: int


@dataclass
class SearchMedicationsQuery(IQuery):
    """Query to search medications."""
    search_term: str = ""
    medication_type: Optional[str] = None
    low_stock_only: bool = False
    expiring_soon: bool = False
    days_until_expiry: int = 30
    pagination: PaginationParams = None


@dataclass
class GetLowStockMedicationsQuery(IQuery):
    """Query to get medications with low stock."""
    warning_level: Optional[int] = None


@dataclass
class GetExpiringMedicationsQuery(IQuery):
    """Query to get expiring medications."""
    days_ahead: int = 30


# Prescription Queries
@dataclass
class GetPrescriptionByIdQuery(IQuery):
    """Query to get prescription by ID."""
    prescription_id: int


@dataclass
class GetPrescriptionsByPatientQuery(IQuery):
    """Query to get prescriptions by patient."""
    patient_id: int
    status: Optional[str] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    pagination: PaginationParams = None


@dataclass
class GetPrescriptionsByDoctorQuery(IQuery):
    """Query to get prescriptions by doctor."""
    doctor_id: int
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    pagination: PaginationParams = None


# Room Queries
@dataclass
class GetRoomByIdQuery(IQuery):
    """Query to get room by ID."""
    room_id: int


@dataclass
class GetAvailableRoomsQuery(IQuery):
    """Query to get available rooms."""
    room_type: Optional[str] = None
    department_id: Optional[int] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None


@dataclass
class GetRoomOccupancyQuery(IQuery):
    """Query to get room occupancy status."""
    department_id: Optional[int] = None
    room_type: Optional[str] = None


# Billing Queries
@dataclass
class GetBillByIdQuery(IQuery):
    """Query to get bill by ID."""
    bill_id: int


@dataclass
class GetBillsByPatientQuery(IQuery):
    """Query to get bills by patient."""
    patient_id: int
    status: Optional[str] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    pagination: PaginationParams = None


@dataclass
class GetUnpaidBillsQuery(IQuery):
    """Query to get unpaid bills."""
    days_overdue: Optional[int] = None
    pagination: PaginationParams = None


# Department Queries
@dataclass
class GetAllDepartmentsQuery(IQuery):
    """Query to get all departments."""
    include_staff_count: bool = False


@dataclass
class GetDepartmentByIdQuery(IQuery):
    """Query to get department by ID."""
    department_id: int
    include_staff: bool = False
    include_rooms: bool = False


# Report Queries
@dataclass
class GetFinancialReportQuery(IQuery):
    """Query to get financial report."""
    date_from: date
    date_to: date
    group_by: str = 'day'  # 'day', 'week', 'month'


@dataclass
class GetPatientStatisticsQuery(IQuery):
    """Query to get patient statistics."""
    date_from: date
    date_to: date
    group_by: str = 'day'


@dataclass
class GetInventoryReportQuery(IQuery):
    """Query to get inventory report."""
    include_expired: bool = True
    include_low_stock: bool = True


@dataclass
class GetDoctorPerformanceQuery(IQuery):
    """Query to get doctor performance metrics."""
    doctor_id: Optional[int] = None
    date_from: date
    date_to: date


# User Queries
@dataclass
class GetUserByIdQuery(IQuery):
    """Query to get user by ID."""
    user_id: int


@dataclass
class GetUserByUsernameQuery(IQuery):
    """Query to get user by username."""
    username: str


@dataclass
class SearchUsersQuery(IQuery):
    """Query to search users."""
    search_term: str = ""
    role: Optional[str] = None
    active_only: bool = True
    pagination: PaginationParams = None
