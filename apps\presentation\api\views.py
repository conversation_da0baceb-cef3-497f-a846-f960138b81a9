"""
REST API ViewSets for Hospital Management System
Implements CRUD operations and business logic endpoints for all domain entities.
"""
from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Sum
from django.utils import timezone
from decimal import Decimal

from apps.domain.models.user import NguoiDung
from apps.domain.models.department import KhoaPhong
from apps.domain.models.patient import <PERSON>h<PERSON>han
from apps.domain.models.medical_staff import NhanVienYTe
from apps.domain.models.room import PhongBenh
from apps.domain.models.appointment import LichHen
from apps.domain.models.medication import Thuoc
from apps.domain.models.prescription import DonThuoc, ChiTietDonThuoc
from apps.domain.models.bill import HoaDon

from .serializers import (
    LoginSerializer, ChangePasswordSerializer, UserSerializer, UserProfileSerializer,
    DepartmentSerializer, DepartmentListSerializer,
    PatientSerializer, Patient<PERSON>reateSerializer, PatientListSerializer,
    MedicalStaffSerializer, MedicalStaffListSerializer,
    RoomSerializer, RoomListSerializer,
    AppointmentSerializer, AppointmentCreateSerializer, AppointmentListSerializer,
    MedicationSerializer, MedicationListSerializer,
    PrescriptionSerializer, PrescriptionCreateSerializer, PrescriptionListSerializer,
    PrescriptionDetailSerializer,
    BillSerializer, BillCreateSerializer, BillListSerializer,
    PaymentProcessSerializer, InsuranceCoverageSerializer,
    DashboardStatsSerializer, RevenueStatsSerializer
)


# Authentication Views
class CustomAuthToken(ObtainAuthToken):
    """Custom authentication token view."""
    
    def post(self, request, *args, **kwargs):
        serializer = LoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        
        return Response({
            'token': token.key,
            'user': UserProfileSerializer(user).data,
            'message': 'Đăng nhập thành công'
        })


# User Management ViewSets
class UserViewSet(viewsets.ModelViewSet):
    """User management ViewSet."""
    
    queryset = NguoiDung.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['vai_tro', 'trang_thai', 'is_active']
    search_fields = ['username', 'ho_ten', 'email']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return UserProfileSerializer
        elif self.action == 'retrieve':
            return UserProfileSerializer
        return UserSerializer
    
    @action(detail=False, methods=['get', 'patch'])
    def profile(self, request):
        """Get or update current user profile."""
        if request.method == 'GET':
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data)
        
        elif request.method == 'PATCH':
            serializer = UserSerializer(request.user, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def change_password(self, request):
        """Change user password."""
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return Response({'message': 'Mật khẩu đã được thay đổi thành công'})


# Department Management ViewSet
class DepartmentViewSet(viewsets.ModelViewSet):
    """Department management ViewSet."""
    
    queryset = KhoaPhong.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['trang_thai', 'tang']
    search_fields = ['ten_khoa_phong', 'truong_khoa']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return DepartmentListSerializer
        return DepartmentSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            staff_count=Count('nhan_vien_y_te'),
            room_count=Count('phong_benh')
        )
    
    @action(detail=True, methods=['get'])
    def staff(self, request, pk=None):
        """Get staff members of the department."""
        department = self.get_object()
        staff = NhanVienYTe.objects.filter(khoa_phong=department)
        serializer = MedicalStaffListSerializer(staff, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def rooms(self, request, pk=None):
        """Get rooms of the department."""
        department = self.get_object()
        rooms = PhongBenh.objects.filter(khoa_phong=department)
        serializer = RoomListSerializer(rooms, many=True)
        return Response(serializer.data)


# Patient Management ViewSet
class PatientViewSet(viewsets.ModelViewSet):
    """Patient management ViewSet."""
    
    queryset = BenhNhan.objects.all()
    serializer_class = PatientSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['trang_thai', 'gioi_tinh', 'loai_bao_hiem']
    search_fields = ['ma_benh_nhan', 'ho_ten', 'so_dien_thoai', 'so_cccd']
    ordering_fields = ['ho_ten', 'ngay_sinh', 'created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return PatientListSerializer
        elif self.action == 'create':
            return PatientCreateSerializer
        return PatientSerializer
    
    @action(detail=True, methods=['get'])
    def appointments(self, request, pk=None):
        """Get patient's appointments."""
        patient = self.get_object()
        appointments = LichHen.objects.filter(benh_nhan=patient).order_by('-ngay_gio_hen')
        serializer = AppointmentListSerializer(appointments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def prescriptions(self, request, pk=None):
        """Get patient's prescriptions."""
        patient = self.get_object()
        prescriptions = DonThuoc.objects.filter(benh_nhan=patient).order_by('-ngay_ke_don')
        serializer = PrescriptionListSerializer(prescriptions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def bills(self, request, pk=None):
        """Get patient's bills."""
        patient = self.get_object()
        bills = HoaDon.objects.filter(benh_nhan=patient).order_by('-ngay_tao')
        serializer = BillListSerializer(bills, many=True)
        return Response(serializer.data)


# Medical Staff ViewSet
class MedicalStaffViewSet(viewsets.ModelViewSet):
    """Medical staff management ViewSet."""
    
    queryset = NhanVienYTe.objects.select_related('khoa_phong')
    serializer_class = MedicalStaffSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['loai_nhan_vien', 'chuc_vu', 'khoa_phong', 'trang_thai']
    search_fields = ['ma_nhan_vien', 'ho_ten', 'so_dien_thoai', 'chuyen_khoa']
    ordering_fields = ['ho_ten', 'chuc_vu', 'ngay_bat_dau']
    ordering = ['ho_ten']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return MedicalStaffListSerializer
        return MedicalStaffSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            experience_years=timezone.now().year - 
            timezone.now().year  # This should be calculated properly based on ngay_bat_dau
        )
    
    @action(detail=True, methods=['get'])
    def appointments(self, request, pk=None):
        """Get staff's appointments."""
        staff = self.get_object()
        if staff.loai_nhan_vien != 'Bac_Si':
            return Response({'error': 'Chỉ bác sĩ mới có lịch hẹn'}, status=400)
        
        appointments = LichHen.objects.filter(bac_si=staff).order_by('-ngay_gio_hen')
        serializer = AppointmentListSerializer(appointments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def schedule(self, request, pk=None):
        """Get staff's schedule for specific date."""
        staff = self.get_object()
        date = request.query_params.get('date', timezone.now().date())
        
        appointments = LichHen.objects.filter(
            bac_si=staff,
            ngay_gio_hen__date=date
        ).order_by('ngay_gio_hen')
        
        serializer = AppointmentListSerializer(appointments, many=True)
        return Response(serializer.data)


# Room Management ViewSet
class RoomViewSet(viewsets.ModelViewSet):
    """Room management ViewSet."""
    
    queryset = PhongBenh.objects.select_related('khoa_phong')
    serializer_class = RoomSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['loai_phong', 'khoa_phong', 'trang_thai', 'tang']
    search_fields = ['ma_phong', 'ten_phong']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return RoomListSerializer
        return RoomSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            occupancy_rate=1.0 - (
                models.F('so_giuong_trong') / models.F('so_giuong')
            )
        )
    
    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available rooms."""
        rooms = self.get_queryset().filter(
            trang_thai='Hoat_Dong',
            so_giuong_trong__gt=0
        )
        serializer = RoomListSerializer(rooms, many=True)
        return Response(serializer.data)


# Appointment Management ViewSet
class AppointmentViewSet(viewsets.ModelViewSet):
    """Appointment management ViewSet."""
    
    queryset = LichHen.objects.select_related('benh_nhan', 'bac_si', 'bac_si__khoa_phong')
    serializer_class = AppointmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['trang_thai', 'loai_kham', 'bac_si', 'benh_nhan']
    search_fields = ['ma_lich_hen', 'benh_nhan__ho_ten', 'bac_si__ho_ten']
    ordering_fields = ['ngay_gio_hen']
    ordering = ['ngay_gio_hen']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return AppointmentListSerializer
        elif self.action == 'create':
            return AppointmentCreateSerializer
        return AppointmentSerializer
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm appointment."""
        appointment = self.get_object()
        try:
            appointment.confirm_appointment()
            return Response({'message': 'Xác nhận lịch hẹn thành công'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel appointment."""
        appointment = self.get_object()
        reason = request.data.get('reason', 'Không có lý do')
        
        try:
            appointment.cancel_appointment(reason)
            return Response({'message': 'Hủy lịch hẹn thành công'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Complete appointment with examination results."""
        appointment = self.get_object()
        results = request.data.get('ket_qua_kham')
        doctor_notes = request.data.get('ghi_chu_bac_si')
        
        try:
            appointment.complete_appointment(results, doctor_notes)
            return Response({'message': 'Hoàn thành khám bệnh thành công'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)


# Medication Management ViewSet
class MedicationViewSet(viewsets.ModelViewSet):
    """Medication management ViewSet."""
    
    queryset = Thuoc.objects.all()
    serializer_class = MedicationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['trang_thai', 'dang_bao_che', 'hang_san_xuat']
    search_fields = ['ma_thuoc', 'ten_thuoc', 'thanh_phan', 'hang_san_xuat']
    ordering_fields = ['ten_thuoc', 'ngay_het_han', 'so_luong_ton_kho']
    ordering = ['ten_thuoc']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return MedicationListSerializer
        return MedicationSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            is_low_stock=models.Case(
                models.When(
                    so_luong_ton_kho__lte=models.F('muc_canh_bao_ton_kho'),
                    then=models.Value(True)
                ),
                default=models.Value(False),
                output_field=models.BooleanField()
            ),
            is_expired=models.Case(
                models.When(
                    ngay_het_han__lt=timezone.now().date(),
                    then=models.Value(True)
                ),
                default=models.Value(False),
                output_field=models.BooleanField()
            ),
            stock_value=models.F('so_luong_ton_kho') * models.F('gia_nhap')
        )
    
    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get medications with low stock."""
        medications = self.get_queryset().filter(
            so_luong_ton_kho__lte=models.F('muc_canh_bao_ton_kho')
        )
        serializer = MedicationListSerializer(medications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def expired(self, request):
        """Get expired medications."""
        medications = self.get_queryset().filter(
            ngay_het_han__lt=timezone.now().date()
        )
        serializer = MedicationListSerializer(medications, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_stock(self, request, pk=None):
        """Add stock to medication."""
        medication = self.get_object()
        quantity = request.data.get('quantity', 0)
        reason = request.data.get('reason', 'Nhập kho')
        
        try:
            quantity = int(quantity)
            medication.add_stock(quantity, reason)
            return Response({
                'message': f'Đã thêm {quantity} {medication.get_don_vi_tinh_display()}',
                'current_stock': medication.so_luong_ton_kho
            })
        except (ValueError, TypeError) as e:
            return Response({'error': str(e)}, status=400)


# Prescription Management ViewSet
class PrescriptionViewSet(viewsets.ModelViewSet):
    """Prescription management ViewSet."""
    
    queryset = DonThuoc.objects.select_related('benh_nhan', 'bac_si', 'duoc_si')
    serializer_class = PrescriptionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['trang_thai', 'loai_don_thuoc', 'bac_si', 'benh_nhan']
    search_fields = ['ma_don_thuoc', 'benh_nhan__ho_ten', 'bac_si__ho_ten', 'chan_doan']
    ordering_fields = ['ngay_ke_don']
    ordering = ['-ngay_ke_don']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return PrescriptionListSerializer
        elif self.action == 'create':
            return PrescriptionCreateSerializer
        return PrescriptionSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            medication_count=Count('chi_tiet_don_thuoc')
        )
    
    @action(detail=True, methods=['post'])
    def issue(self, request, pk=None):
        """Issue prescription."""
        prescription = self.get_object()
        try:
            prescription.issue_prescription()
            return Response({'message': 'Phát hành đơn thuốc thành công'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def dispense(self, request, pk=None):
        """Dispense prescription."""
        prescription = self.get_object()
        pharmacist_id = request.data.get('pharmacist_id')
        notes = request.data.get('notes', '')
        
        if not pharmacist_id:
            return Response({'error': 'Thiếu thông tin dược sĩ'}, status=400)
        
        try:
            pharmacist = NhanVienYTe.objects.get(NhanVienID=pharmacist_id)
            prescription.dispense_prescription(pharmacist, notes)
            return Response({'message': 'Cấp thuốc thành công'})
        except NhanVienYTe.DoesNotExist:
            return Response({'error': 'Không tìm thấy dược sĩ'}, status=400)
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def add_medication(self, request, pk=None):
        """Add medication to prescription."""
        prescription = self.get_object()
        medication_id = request.data.get('medication_id')
        quantity = request.data.get('quantity')
        dosage = request.data.get('dosage')
        frequency = request.data.get('frequency')
        duration = request.data.get('duration')
        instructions = request.data.get('instructions', '')
        
        try:
            medication = Thuoc.objects.get(ThuocID=medication_id)
            detail = prescription.add_medication(
                medication, int(quantity), dosage, frequency, duration, instructions
            )
            serializer = PrescriptionDetailSerializer(detail)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Thuoc.DoesNotExist:
            return Response({'error': 'Không tìm thấy thuốc'}, status=400)
        except ValueError as e:
            return Response({'error': str(e)}, status=400)


# Bill Management ViewSet
class BillViewSet(viewsets.ModelViewSet):
    """Bill management ViewSet."""
    
    queryset = HoaDon.objects.select_related('benh_nhan', 'lich_hen', 'don_thuoc')
    serializer_class = BillSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['trang_thai', 'phuong_thuc_thanh_toan', 'benh_nhan']
    search_fields = ['ma_hoa_don', 'benh_nhan__ho_ten']
    ordering_fields = ['ngay_tao', 'tong_tien']
    ordering = ['-ngay_tao']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return BillListSerializer
        elif self.action == 'create':
            return BillCreateSerializer
        return BillSerializer
    
    @action(detail=True, methods=['post'])
    def issue(self, request, pk=None):
        """Issue bill."""
        bill = self.get_object()
        try:
            bill.issue_bill()
            return Response({'message': 'Phát hành hóa đơn thành công'})
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def process_payment(self, request, pk=None):
        """Process payment for bill."""
        bill = self.get_object()
        serializer = PaymentProcessSerializer(data=request.data, context={'bill': bill})
        serializer.is_valid(raise_exception=True)
        
        try:
            bill.process_payment(
                amount=serializer.validated_data['amount'],
                payment_method=serializer.validated_data['payment_method'],
                notes=serializer.validated_data.get('notes')
            )
            return Response({
                'message': 'Thanh toán thành công',
                'remaining_balance': float(bill.con_no)
            })
        except ValueError as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def apply_insurance(self, request, pk=None):
        """Apply insurance coverage to bill."""
        bill = self.get_object()
        serializer = InsuranceCoverageSerializer(data=request.data, context={'bill': bill})
        serializer.is_valid(raise_exception=True)
        
        try:
            bill.apply_insurance_coverage(
                coverage_amount=serializer.validated_data['coverage_amount'],
                notes=serializer.validated_data.get('notes')
            )
            return Response({
                'message': 'Áp dụng bảo hiểm thành công',
                'remaining_balance': float(bill.con_no)
            })
        except ValueError as e:
            return Response({'error': str(e)}, status=400)


# Dashboard and Statistics ViewSet
class DashboardViewSet(viewsets.ViewSet):
    """Dashboard statistics ViewSet."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get dashboard statistics."""
        today = timezone.now().date()
        
        stats = {
            'total_patients': BenhNhan.objects.filter(trang_thai='Hoat_Dong').count(),
            'total_appointments_today': LichHen.objects.filter(
                ngay_gio_hen__date=today
            ).count(),
            'total_revenue_today': HoaDon.objects.filter(
                ngay_tao__date=today,
                trang_thai='Paid'
            ).aggregate(
                total=Sum('tong_tien')
            )['total'] or 0,
            'pending_bills': HoaDon.objects.filter(
                trang_thai__in=['Issued', 'Partially_Paid', 'Overdue']
            ).count(),
            'low_stock_medications': Thuoc.objects.filter(
                so_luong_ton_kho__lte=models.F('muc_canh_bao_ton_kho')
            ).count(),
            'active_staff': NhanVienYTe.objects.filter(trang_thai='Hoat_Dong').count(),
            'available_rooms': PhongBenh.objects.filter(
                trang_thai='Hoat_Dong',
                so_giuong_trong__gt=0
            ).count()
        }
        
        serializer = DashboardStatsSerializer(stats)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def revenue(self, request):
        """Get revenue statistics."""
        from datetime import datetime, timedelta
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        revenue_data = HoaDon.objects.filter(
            ngay_tao__date__range=[start_date, end_date],
            trang_thai='Paid'
        ).values('ngay_tao__date').annotate(
            total_revenue=Sum('tong_tien'),
            examination_revenue=Sum('phi_kham'),
            medication_revenue=Sum('phi_thuoc'),
            service_revenue=Sum('phi_dich_vu')
        ).order_by('ngay_tao__date')
        
        serializer = RevenueStatsSerializer(revenue_data, many=True)
        return Response(serializer.data)
