"""
Medication Domain Model
Defines the Medication entity for hospital medication management.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from decimal import Decimal
from datetime import date


class Thuoc(BaseEntity):
    """
    Medication model for hospital management system.
    Represents medications and drugs used in the hospital.
    """
    
    LOAI_THUOC_CHOICES = [
        ('Thuoc_Vien', 'Thuốc viên'),
        ('<PERSON>hu<PERSON>_Nuoc', 'Thuốc nước'),
        ('<PERSON>hu<PERSON>_Tiem', 'Thuốc tiêm'),
        ('<PERSON>hu<PERSON>_Boi', 'Thuốc bôi'),
        ('<PERSON>hu<PERSON>_<PERSON><PERSON>', 'Thuốc nhỏ'),
        ('Thu<PERSON>_Xit', 'Thuốc xịt'),
        ('Thu<PERSON>_<PERSON>', 'Thuốc cao'),
        ('<PERSON>hac', 'Khác'),
    ]
    
    DON_VI_CHOICES = [
        ('<PERSON><PERSON>', 'Viên'),
        ('<PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON>', '<PERSON><PERSON>'),
        ('<PERSON><PERSON>', '<PERSON>ý<PERSON>'),
        ('Go<PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON>', '<PERSON><PERSON>'),
        ('Ong', 'Ống'),
        ('Kg', 'Kilogram'),
        ('Gram', 'Gram'),
        ('Ml', 'Milliliter'),
        ('Lit', 'Lít'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('Available', 'Có sẵn'),
        ('Low_Stock', 'Sắp hết'),
        ('Out_Of_Stock', 'Hết hàng'),
        ('Expired', 'Hết hạn'),
        ('Discontinued', 'Ngừng sử dụng'),
    ]
    
    # Primary Key
    ThuocID = models.AutoField(primary_key=True)
    
    # Basic Information
    ten_thuoc = models.CharField(
        'Tên thuốc',
        max_length=200,
        help_text='Tên thương mại của thuốc'
    )
    
    ten_hoat_chat = models.CharField(
        'Tên hoạt chất',
        max_length=200,
        help_text='Tên hoạt chất chính'
    )
    
    ma_thuoc = models.CharField(
        'Mã thuốc',
        max_length=50,
        unique=True,
        help_text='Mã định danh duy nhất của thuốc'
    )
    
    loai_thuoc = models.CharField(
        'Loại thuốc',
        max_length=20,
        choices=LOAI_THUOC_CHOICES,
        help_text='Dạng bào chế của thuốc'
    )
    
    # Manufacturer Information
    nha_san_xuat = models.CharField(
        'Nhà sản xuất',
        max_length=200,
        help_text='Tên nhà sản xuất thuốc'
    )
    
    nuoc_san_xuat = models.CharField(
        'Nước sản xuất',
        max_length=100,
        help_text='Nước sản xuất thuốc'
    )
    
    # Content and Dosage
    ham_luong = models.CharField(
        'Hàm lượng',
        max_length=100,
        help_text='Hàm lượng hoạt chất'
    )
    
    quy_cach_dong_goi = models.CharField(
        'Quy cách đóng gói',
        max_length=200,
        help_text='Quy cách đóng gói của thuốc'
    )
    
    don_vi_tinh = models.CharField(
        'Đơn vị tính',
        max_length=20,
        choices=DON_VI_CHOICES,
        help_text='Đơn vị tính của thuốc'
    )
    
    # Stock Information
    so_luong_ton_kho = models.IntegerField(
        'Số lượng tồn kho',
        default=0,
        help_text='Số lượng hiện có trong kho'
    )
    
    so_luong_toi_thieu = models.IntegerField(
        'Số lượng tối thiểu',
        default=10,
        help_text='Số lượng tối thiểu cần duy trì'
    )
    
    # Expiry Information
    ngay_san_xuat = models.DateField(
        'Ngày sản xuất',
        help_text='Ngày sản xuất thuốc'
    )
    
    ngay_het_han = models.DateField(
        'Ngày hết hạn',
        help_text='Ngày hết hạn sử dụng'
    )
    
    # Pricing
    gia_nhap = models.DecimalField(
        'Giá nhập',
        max_digits=12,
        decimal_places=0,
        help_text='Giá nhập thuốc (VND)'
    )
    
    gia_ban = models.DecimalField(
        'Giá bán',
        max_digits=12,
        decimal_places=0,
        help_text='Giá bán thuốc (VND)'
    )
    
    # Medical Information
    chi_dinh = models.TextField(
        'Chỉ định',
        blank=True,
        null=True,
        help_text='Các chỉ định sử dụng thuốc'
    )
    
    chong_chi_dinh = models.TextField(
        'Chống chỉ định',
        blank=True,
        null=True,
        help_text='Các chống chỉ định khi sử dụng'
    )
    
    cach_dung = models.TextField(
        'Cách dùng',
        blank=True,
        null=True,
        help_text='Hướng dẫn cách sử dụng thuốc'
    )
    
    tac_dung_phu = models.TextField(
        'Tác dụng phụ',
        blank=True,
        null=True,
        help_text='Các tác dụng phụ có thể xảy ra'
    )
    
    # Status and Notes
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Available',
        help_text='Trạng thái hiện tại của thuốc'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú thêm về thuốc'
    )
    
    # Timestamps
    ngay_nhap_kho = models.DateTimeField(
        'Ngày nhập kho',
        default=timezone.now,
        help_text='Ngày nhập thuốc vào kho'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật thông tin lần cuối'
    )
    
    class Meta:
        db_table = 'Thuoc'
        verbose_name = 'Thuốc'
        verbose_name_plural = 'Thuốc'
        ordering = ['ten_thuoc']
        indexes = [
            models.Index(fields=['ma_thuoc']),
            models.Index(fields=['ten_thuoc']),
            models.Index(fields=['ten_hoat_chat']),
            models.Index(fields=['loai_thuoc']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['ngay_het_han']),
            models.Index(fields=['so_luong_ton_kho']),
            models.Index(fields=['nha_san_xuat']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(so_luong_ton_kho__gte=0),
                name='check_non_negative_stock'
            ),
            models.CheckConstraint(
                check=models.Q(so_luong_toi_thieu__gte=0),
                name='check_non_negative_min_stock'
            ),
            models.CheckConstraint(
                check=models.Q(gia_nhap__gt=0),
                name='check_positive_import_price'
            ),
            models.CheckConstraint(
                check=models.Q(gia_ban__gt=0),
                name='check_positive_sell_price'
            ),
            models.CheckConstraint(
                check=models.Q(ngay_het_han__gt=models.F('ngay_san_xuat')),
                name='check_expiry_after_manufacture'
            ),
        ]
    
    def __str__(self):
        return f"{self.ten_thuoc} ({self.ma_thuoc}) - {self.ham_luong}"
    
    def save(self, *args, **kwargs):
        """Override save to generate medication code and update status."""
        # Auto-generate medication code if not provided
        if not self.ma_thuoc:
            self.ma_thuoc = self.generate_medication_code()
        
        # Auto-update status based on stock and expiry
        self.update_status()
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate medication fields."""
        # Validate expiry date
        if self.ngay_het_han <= self.ngay_san_xuat:
            raise ValidationError({
                'ngay_het_han': 'Ngày hết hạn phải sau ngày sản xuất'
            })
        
        # Validate pricing
        if self.gia_ban < self.gia_nhap:
            raise ValidationError({
                'gia_ban': 'Giá bán không nên thấp hơn giá nhập'
            })
        
        # Validate stock levels
        if self.so_luong_toi_thieu > 1000:
            raise ValidationError({
                'so_luong_toi_thieu': 'Số lượng tối thiểu quá lớn'
            })
    
    def generate_medication_code(self) -> str:
        """Generate unique medication code."""
        # Format: MED + Type prefix + sequential number
        type_prefixes = {
            'Thuoc_Vien': 'TV',
            'Thuoc_Nuoc': 'TN',
            'Thuoc_Tiem': 'TT',
            'Thuoc_Boi': 'TB',
            'Thuoc_Nhang': 'TNH',
            'Thuoc_Xit': 'TX',
            'Thuoc_Cao': 'TC',
            'Khac': 'TK'
        }
        
        prefix = f"MED{type_prefixes.get(self.loai_thuoc, 'TK')}"
        
        # Find highest sequential number
        existing_codes = Thuoc.objects.filter(
            ma_thuoc__startswith=prefix
        ).values_list('ma_thuoc', flat=True)
        
        max_seq = 0
        for code in existing_codes:
            try:
                seq = int(code[len(prefix):])
                max_seq = max(max_seq, seq)
            except (ValueError, IndexError):
                continue
        
        return f"{prefix}{max_seq + 1:06d}"
    
    def update_status(self):
        """Update medication status based on stock and expiry."""
        # Check if expired
        if self.is_expired():
            self.trang_thai = 'Expired'
        # Check stock levels
        elif self.so_luong_ton_kho == 0:
            self.trang_thai = 'Out_Of_Stock'
        elif self.is_low_stock():
            self.trang_thai = 'Low_Stock'
        else:
            self.trang_thai = 'Available'
    
    # Business Methods
    def is_available(self) -> bool:
        """Check if medication is available."""
        return self.trang_thai == 'Available' and self.so_luong_ton_kho > 0
    
    def is_expired(self) -> bool:
        """Check if medication is expired."""
        return self.ngay_het_han <= date.today()
    
    def is_low_stock(self) -> bool:
        """Check if medication is low in stock."""
        return self.so_luong_ton_kho <= self.so_luong_toi_thieu
    
    def is_out_of_stock(self) -> bool:
        """Check if medication is out of stock."""
        return self.so_luong_ton_kho == 0
    
    def is_expiring_soon(self, days: int = 30) -> bool:
        """Check if medication is expiring within specified days."""
        from datetime import timedelta
        expiry_threshold = date.today() + timedelta(days=days)
        return self.ngay_het_han <= expiry_threshold
    
    def can_dispense(self, quantity: int) -> bool:
        """Check if medication can be dispensed in requested quantity."""
        return (self.is_available() and 
                not self.is_expired() and 
                self.so_luong_ton_kho >= quantity)
    
    def get_shelf_life_days(self) -> int:
        """Get total shelf life in days."""
        return (self.ngay_het_han - self.ngay_san_xuat).days
    
    def get_remaining_shelf_life_days(self) -> int:
        """Get remaining shelf life in days."""
        if self.is_expired():
            return 0
        return (self.ngay_het_han - date.today()).days
    
    def get_profit_margin(self) -> Decimal:
        """Calculate profit margin percentage."""
        if self.gia_nhap == 0:
            return Decimal('0')
        
        profit = self.gia_ban - self.gia_nhap
        return (profit / self.gia_nhap) * 100
    
    def get_total_value(self) -> Decimal:
        """Get total inventory value."""
        return self.gia_ban * self.so_luong_ton_kho
    
    def add_stock(self, quantity: int, notes: str = None):
        """Add stock to medication."""
        if quantity <= 0:
            raise ValueError("Số lượng phải lớn hơn 0")
        
        old_quantity = self.so_luong_ton_kho
        self.so_luong_ton_kho += quantity
        
        # Update status after stock change
        self.update_status()
        
        if notes:
            self.ghi_chu = f"{self.ghi_chu or ''}\n{notes}".strip()
        
        self.save(update_fields=['so_luong_ton_kho', 'trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_stock_added_event(old_quantity, self.so_luong_ton_kho, quantity)
    
    def reduce_stock(self, quantity: int, notes: str = None):
        """Reduce stock from medication."""
        if quantity <= 0:
            raise ValueError("Số lượng phải lớn hơn 0")
        
        if quantity > self.so_luong_ton_kho:
            raise ValueError("Không đủ thuốc trong kho")
        
        old_quantity = self.so_luong_ton_kho
        self.so_luong_ton_kho -= quantity
        
        # Update status after stock change
        self.update_status()
        
        if notes:
            self.ghi_chu = f"{self.ghi_chu or ''}\n{notes}".strip()
        
        self.save(update_fields=['so_luong_ton_kho', 'trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_stock_reduced_event(old_quantity, self.so_luong_ton_kho, quantity)
    
    def update_pricing(self, import_price: Decimal, sell_price: Decimal):
        """Update medication pricing."""
        old_import_price = self.gia_nhap
        old_sell_price = self.gia_ban
        
        self.gia_nhap = import_price
        self.gia_ban = sell_price
        
        self.save(update_fields=['gia_nhap', 'gia_ban', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_pricing_updated_event(
            old_import_price, import_price, old_sell_price, sell_price
        )
    
    def mark_expired(self):
        """Mark medication as expired."""
        if not self.is_expired():
            raise ValueError("Thuốc chưa hết hạn")
        
        old_status = self.trang_thai
        self.trang_thai = 'Expired'
        self.ghi_chu = f"{self.ghi_chu or ''}\nĐã hết hạn vào {date.today()}".strip()
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_expired_event()
    
    def discontinue_medication(self, reason: str):
        """Discontinue medication."""
        old_status = self.trang_thai
        self.trang_thai = 'Discontinued'
        self.ghi_chu = f"{self.ghi_chu or ''}\nNgừng sử dụng: {reason}".strip()
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_discontinued_event(reason)
    
    def reactivate_medication(self):
        """Reactivate discontinued medication."""
        if self.trang_thai != 'Discontinued':
            raise ValueError("Chỉ có thể kích hoạt lại thuốc đã ngừng sử dụng")
        
        # Update status based on current conditions
        self.update_status()
        
        self.ghi_chu = f"{self.ghi_chu or ''}\nĐã kích hoạt lại vào {date.today()}".strip()
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_medication_reactivated_event()
    
    @property
    def display_name(self) -> str:
        """Get display name with dosage."""
        return f"{self.ten_thuoc} {self.ham_luong}"
    
    @property
    def stock_status(self) -> dict:
        """Get detailed stock status."""
        return {
            'current_stock': self.so_luong_ton_kho,
            'minimum_stock': self.so_luong_toi_thieu,
            'is_low_stock': self.is_low_stock(),
            'is_out_of_stock': self.is_out_of_stock(),
            'status': self.get_trang_thai_display(),
            'unit': self.get_don_vi_tinh_display()
        }
    
    @property
    def expiry_info(self) -> dict:
        """Get expiry information."""
        return {
            'manufacture_date': self.ngay_san_xuat,
            'expiry_date': self.ngay_het_han,
            'is_expired': self.is_expired(),
            'is_expiring_soon': self.is_expiring_soon(),
            'remaining_days': self.get_remaining_shelf_life_days(),
            'total_shelf_life': self.get_shelf_life_days()
        }
    
    @property
    def pricing_info(self) -> dict:
        """Get pricing information."""
        return {
            'import_price': float(self.gia_nhap),
            'sell_price': float(self.gia_ban),
            'profit_margin': float(self.get_profit_margin()),
            'total_value': float(self.get_total_value())
        }
    
    # Domain Events
    def raise_medication_created_event(self):
        """Raise medication created domain event."""
        from apps.domain.events.domain_events import MedicationCreatedEvent
        event = MedicationCreatedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            active_ingredient=self.ten_hoat_chat,
            medication_type=self.loai_thuoc,
            manufacturer=self.nha_san_xuat,
            initial_stock=self.so_luong_ton_kho,
            created_at=self.ngay_nhap_kho
        )
        self.add_domain_event(event)
    
    def raise_medication_stock_added_event(self, old_quantity: int, new_quantity: int, added_quantity: int):
        """Raise medication stock added domain event."""
        from apps.domain.events.domain_events import MedicationStockAddedEvent
        event = MedicationStockAddedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            added_quantity=added_quantity,
            added_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_stock_reduced_event(self, old_quantity: int, new_quantity: int, reduced_quantity: int):
        """Raise medication stock reduced domain event."""
        from apps.domain.events.domain_events import MedicationStockReducedEvent
        event = MedicationStockReducedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            reduced_quantity=reduced_quantity,
            reduced_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_low_stock_event(self):
        """Raise medication low stock domain event."""
        from apps.domain.events.domain_events import MedicationLowStockEvent
        event = MedicationLowStockEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            current_stock=self.so_luong_ton_kho,
            minimum_stock=self.so_luong_toi_thieu,
            detected_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_expired_event(self):
        """Raise medication expired domain event."""
        from apps.domain.events.domain_events import MedicationExpiredEvent
        event = MedicationExpiredEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            expiry_date=self.ngay_het_han,
            current_stock=self.so_luong_ton_kho,
            expired_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_pricing_updated_event(self, old_import_price: Decimal, new_import_price: Decimal,
                                             old_sell_price: Decimal, new_sell_price: Decimal):
        """Raise medication pricing updated domain event."""
        from apps.domain.events.domain_events import MedicationPricingUpdatedEvent
        event = MedicationPricingUpdatedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            old_import_price=float(old_import_price),
            new_import_price=float(new_import_price),
            old_sell_price=float(old_sell_price),
            new_sell_price=float(new_sell_price),
            updated_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_discontinued_event(self, reason: str):
        """Raise medication discontinued domain event."""
        from apps.domain.events.domain_events import MedicationDiscontinuedEvent
        event = MedicationDiscontinuedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            discontinuation_reason=reason,
            discontinued_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_medication_reactivated_event(self):
        """Raise medication reactivated domain event."""
        from apps.domain.events.domain_events import MedicationReactivatedEvent
        event = MedicationReactivatedEvent(
            medication_id=self.ThuocID,
            medication_code=self.ma_thuoc,
            medication_name=self.ten_thuoc,
            reactivated_at=timezone.now()
        )
        self.add_domain_event(event)
