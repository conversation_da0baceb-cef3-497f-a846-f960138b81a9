"""
Custom Logging Middleware
Provides structured logging and request/response tracking for the hospital management system.
"""
import logging
import time
import uuid
from typing import Callable
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin
import json


class LoggingMiddleware(MiddlewareMixin):
    """
    Middleware for logging HTTP requests and responses.
    Implements cross-cutting concerns for monitoring and debugging.
    """
    
    def __init__(self, get_response: Callable = None):
        super().__init__(get_response)
        self.logger = logging.getLogger('hospital_management.requests')
    
    def process_request(self, request: HttpRequest) -> None:
        """Process incoming request and add correlation ID."""
        # Generate correlation ID for request tracking
        correlation_id = str(uuid.uuid4())
        request.correlation_id = correlation_id
        request.start_time = time.time()
        
        # Log request details
        self.logger.info(
            "Request started",
            extra={
                'correlation_id': correlation_id,
                'method': request.method,
                'path': request.path,
                'user': getattr(request.user, 'username', 'anonymous') if hasattr(request, 'user') else 'anonymous',
                'ip_address': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'content_type': request.META.get('CONTENT_TYPE', ''),
                'content_length': request.META.get('CONTENT_LENGTH', 0)
            }
        )
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Process outgoing response and log completion."""
        if hasattr(request, 'correlation_id') and hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # Add correlation ID to response headers
            response['X-Correlation-ID'] = request.correlation_id
            
            # Log response details
            self.logger.info(
                "Request completed",
                extra={
                    'correlation_id': request.correlation_id,
                    'method': request.method,
                    'path': request.path,
                    'status_code': response.status_code,
                    'duration_ms': round(duration * 1000, 2),
                    'response_size': len(response.content) if hasattr(response, 'content') else 0,
                    'user': getattr(request.user, 'username', 'anonymous') if hasattr(request, 'user') else 'anonymous'
                }
            )
            
            # Log slow requests
            if duration > 2.0:  # More than 2 seconds
                self.logger.warning(
                    "Slow request detected",
                    extra={
                        'correlation_id': request.correlation_id,
                        'method': request.method,
                        'path': request.path,
                        'duration_ms': round(duration * 1000, 2),
                        'user': getattr(request.user, 'username', 'anonymous') if hasattr(request, 'user') else 'anonymous'
                    }
                )
        
        return response
    
    def process_exception(self, request: HttpRequest, exception: Exception) -> None:
        """Process exceptions and log errors."""
        if hasattr(request, 'correlation_id'):
            self.logger.error(
                "Request failed with exception",
                extra={
                    'correlation_id': request.correlation_id,
                    'method': request.method,
                    'path': request.path,
                    'exception_type': exception.__class__.__name__,
                    'exception_message': str(exception),
                    'user': getattr(request.user, 'username', 'anonymous') if hasattr(request, 'user') else 'anonymous'
                },
                exc_info=True
            )
    
    def get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class APILoggingMiddleware(MiddlewareMixin):
    """
    Specialized middleware for API request/response logging.
    Logs API payloads for debugging and audit purposes.
    """
    
    def __init__(self, get_response: Callable = None):
        super().__init__(get_response)
        self.logger = logging.getLogger('hospital_management.api')
        self.sensitive_fields = {'password', 'mat_khau', 'token', 'authorization', 'cvv', 'card_number'}
    
    def process_request(self, request: HttpRequest) -> None:
        """Log API request payload."""
        if request.path.startswith('/api/'):
            try:
                if request.content_type == 'application/json' and request.body:
                    try:
                        payload = json.loads(request.body.decode('utf-8'))
                        sanitized_payload = self.sanitize_payload(payload)
                        
                        self.logger.debug(
                            "API Request payload",
                            extra={
                                'correlation_id': getattr(request, 'correlation_id', 'unknown'),
                                'method': request.method,
                                'path': request.path,
                                'payload': sanitized_payload
                            }
                        )
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        self.logger.debug(
                            "API Request with non-JSON payload",
                            extra={
                                'correlation_id': getattr(request, 'correlation_id', 'unknown'),
                                'method': request.method,
                                'path': request.path,
                                'content_type': request.content_type
                            }
                        )
            except Exception as e:
                self.logger.warning(f"Failed to log API request: {str(e)}")
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Log API response payload."""
        if request.path.startswith('/api/') and hasattr(response, 'content'):
            try:
                if response.get('Content-Type', '').startswith('application/json'):
                    try:
                        payload = json.loads(response.content.decode('utf-8'))
                        sanitized_payload = self.sanitize_payload(payload)
                        
                        self.logger.debug(
                            "API Response payload",
                            extra={
                                'correlation_id': getattr(request, 'correlation_id', 'unknown'),
                                'method': request.method,
                                'path': request.path,
                                'status_code': response.status_code,
                                'payload': sanitized_payload
                            }
                        )
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        pass  # Skip non-JSON responses
            except Exception as e:
                self.logger.warning(f"Failed to log API response: {str(e)}")
        
        return response
    
    def sanitize_payload(self, payload) -> dict:
        """Remove sensitive information from payload."""
        if isinstance(payload, dict):
            sanitized = {}
            for key, value in payload.items():
                if key.lower() in self.sensitive_fields:
                    sanitized[key] = "***REDACTED***"
                elif isinstance(value, dict):
                    sanitized[key] = self.sanitize_payload(value)
                elif isinstance(value, list):
                    sanitized[key] = [self.sanitize_payload(item) if isinstance(item, dict) else item for item in value]
                else:
                    sanitized[key] = value
            return sanitized
        return payload


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to all responses.
    Implements security best practices for web applications.
    """
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Add security headers to response."""
        # Prevent clickjacking
        response['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response['X-Content-Type-Options'] = 'nosniff'
        
        # Enable XSS protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Add Content Security Policy for API responses
        if request.path.startswith('/api/'):
            response['Content-Security-Policy'] = "default-src 'none'; frame-ancestors 'none';"
        
        # Add correlation ID if available
        if hasattr(request, 'correlation_id'):
            response['X-Correlation-ID'] = request.correlation_id
        
        return response
