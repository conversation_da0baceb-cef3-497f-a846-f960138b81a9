"""
Notification Service Implementation
Provides messaging infrastructure for sending notifications across multiple channels.
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from django.conf import settings
from django.template.loader import render_to_string
from apps.domain.shared.interfaces import INotificationService
from apps.application.strategies.notification_strategies import (
    EmailNotificationStrategy, SMSNotificationStrategy,
    PushNotificationStrategy, InAppNotificationStrategy
)


logger = logging.getLogger('hospital_management.notifications')


class NotificationPriority(Enum):
    """Notification priority levels."""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(Enum):
    """Notification delivery status."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRY = "retry"


@dataclass
class NotificationMessage:
    """Notification message data structure."""
    recipient: str
    subject: str
    content: str
    notification_type: str
    channel: str
    priority: NotificationPriority = NotificationPriority.MEDIUM
    template_name: str = None
    template_data: Dict[str, Any] = None
    scheduled_at: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    status: NotificationStatus = NotificationStatus.PENDING
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.template_data is None:
            self.template_data = {}
        if self.metadata is None:
            self.metadata = {}


class NotificationService(INotificationService):
    """
    Multi-channel notification service implementation.
    Supports email, SMS, push notifications, and in-app notifications.
    """
    
    def __init__(self):
        self.strategies = {
            'email': EmailNotificationStrategy(),
            'sms': SMSNotificationStrategy(),
            'push': PushNotificationStrategy(),
            'in_app': InAppNotificationStrategy()
        }
        self.notification_queue = []
    
    async def send_notification(self, message: NotificationMessage) -> bool:
        """Send notification through specified channel."""
        try:
            if message.channel not in self.strategies:
                logger.error(f"Unsupported notification channel: {message.channel}")
                return False
            
            strategy = self.strategies[message.channel]
            
            # Render template if specified
            if message.template_name:
                message.content = self._render_template(message.template_name, message.template_data)
            
            # Send notification
            success = await strategy.send_notification(
                recipient=message.recipient,
                subject=message.subject,
                content=message.content,
                priority=message.priority.value,
                metadata=message.metadata
            )
            
            if success:
                message.status = NotificationStatus.SENT
                logger.info(f"Notification sent successfully via {message.channel} to {message.recipient}")
            else:
                message.status = NotificationStatus.FAILED
                logger.error(f"Failed to send notification via {message.channel} to {message.recipient}")
            
            return success
            
        except Exception as e:
            logger.error(f"Notification service error: {str(e)}")
            message.status = NotificationStatus.FAILED
            return False
    
    async def send_multi_channel_notification(self, base_message: NotificationMessage, 
                                            channels: List[str]) -> Dict[str, bool]:
        """Send notification across multiple channels."""
        results = {}
        
        for channel in channels:
            # Create channel-specific message
            channel_message = NotificationMessage(
                recipient=base_message.recipient,
                subject=base_message.subject,
                content=base_message.content,
                notification_type=base_message.notification_type,
                channel=channel,
                priority=base_message.priority,
                template_name=base_message.template_name,
                template_data=base_message.template_data.copy(),
                metadata=base_message.metadata.copy()
            )
            
            results[channel] = await self.send_notification(channel_message)
        
        return results
    
    async def schedule_notification(self, message: NotificationMessage, 
                                  scheduled_at: datetime) -> bool:
        """Schedule notification for future delivery."""
        message.scheduled_at = scheduled_at
        message.status = NotificationStatus.PENDING
        
        # In a real implementation, this would be stored in database or queue
        self.notification_queue.append(message)
        
        logger.info(f"Notification scheduled for {scheduled_at} via {message.channel}")
        return True
    
    async def process_scheduled_notifications(self) -> int:
        """Process notifications scheduled for current time."""
        current_time = datetime.now()
        processed_count = 0
        
        # Find notifications ready to send
        ready_notifications = [
            msg for msg in self.notification_queue 
            if msg.scheduled_at and msg.scheduled_at <= current_time and msg.status == NotificationStatus.PENDING
        ]
        
        for message in ready_notifications:
            success = await self.send_notification(message)
            if success:
                processed_count += 1
                self.notification_queue.remove(message)
            elif message.retry_count < message.max_retries:
                message.retry_count += 1
                message.status = NotificationStatus.RETRY
                logger.info(f"Notification retry {message.retry_count} scheduled for {message.recipient}")
        
        return processed_count
    
    def _render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render notification template with context data."""
        try:
            return render_to_string(f"notifications/{template_name}.html", context)
        except Exception as e:
            logger.error(f"Template rendering error: {str(e)}")
            return context.get('fallback_content', 'Notification content unavailable')


class HospitalNotificationService(NotificationService):
    """
    Hospital-specific notification service with predefined notification types.
    Provides convenient methods for common hospital notifications.
    """
    
    # Appointment Notifications
    async def send_appointment_confirmation(self, patient_email: str, patient_name: str,
                                          doctor_name: str, appointment_date: str,
                                          appointment_time: str) -> bool:
        """Send appointment confirmation notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Xác nhận lịch hẹn khám bệnh",
            content="",
            notification_type="appointment_confirmation",
            channel="email",
            priority=NotificationPriority.HIGH,
            template_name="appointment_confirmation",
            template_data={
                'patient_name': patient_name,
                'doctor_name': doctor_name,
                'appointment_date': appointment_date,
                'appointment_time': appointment_time,
                'hospital_name': 'Bệnh viện Đa khoa',
                'contact_phone': '(028) 1234 5678'
            }
        )
        
        return await self.send_notification(message)
    
    async def send_appointment_reminder(self, patient_email: str, patient_phone: str,
                                      patient_name: str, doctor_name: str,
                                      appointment_date: str, appointment_time: str) -> Dict[str, bool]:
        """Send appointment reminder via email and SMS."""
        base_message = NotificationMessage(
            recipient=patient_email,  # Will be overridden for SMS
            subject="Nhắc nhở lịch hẹn khám bệnh",
            content="",
            notification_type="appointment_reminder", 
            channel="email",
            priority=NotificationPriority.HIGH,
            template_name="appointment_reminder",
            template_data={
                'patient_name': patient_name,
                'doctor_name': doctor_name,
                'appointment_date': appointment_date,
                'appointment_time': appointment_time,
                'hospital_name': 'Bệnh viện Đa khoa'
            }
        )
        
        # Send via email and SMS
        results = {}
        results['email'] = await self.send_notification(base_message)
        
        # SMS notification with phone number
        sms_message = NotificationMessage(
            recipient=patient_phone,
            subject="Nhắc nhở lịch hẹn",
            content=f"Xin chào {patient_name}, bạn có lịch hẹn với BS {doctor_name} vào {appointment_time} ngày {appointment_date}. Vui lòng đến đúng giờ.",
            notification_type="appointment_reminder",
            channel="sms",
            priority=NotificationPriority.HIGH
        )
        results['sms'] = await self.send_notification(sms_message)
        
        return results
    
    async def send_appointment_cancellation(self, patient_email: str, patient_name: str,
                                          appointment_date: str, reason: str) -> bool:
        """Send appointment cancellation notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Thông báo hủy lịch hẹn",
            content="",
            notification_type="appointment_cancellation",
            channel="email",
            priority=NotificationPriority.URGENT,
            template_name="appointment_cancellation",
            template_data={
                'patient_name': patient_name,
                'appointment_date': appointment_date,
                'cancellation_reason': reason,
                'hospital_name': 'Bệnh viện Đa khoa',
                'contact_phone': '(028) 1234 5678'
            }
        )
        
        return await self.send_notification(message)
    
    # Prescription Notifications
    async def send_prescription_ready(self, patient_email: str, patient_name: str,
                                    prescription_id: str, pharmacy_name: str) -> bool:
        """Send prescription ready for pickup notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Đơn thuốc đã sẵn sàng",
            content="",
            notification_type="prescription_ready",
            channel="email",
            priority=NotificationPriority.MEDIUM,
            template_name="prescription_ready",
            template_data={
                'patient_name': patient_name,
                'prescription_id': prescription_id,
                'pharmacy_name': pharmacy_name,
                'pickup_hours': '8:00 - 17:00 (Thứ 2 - Chủ nhật)'
            }
        )
        
        return await self.send_notification(message)
    
    # Bill Notifications
    async def send_bill_notification(self, patient_email: str, patient_name: str,
                                   bill_id: str, total_amount: float, due_date: str) -> bool:
        """Send bill notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Thông báo thanh toán viện phí",
            content="",
            notification_type="bill_notification",
            channel="email",
            priority=NotificationPriority.HIGH,
            template_name="bill_notification",
            template_data={
                'patient_name': patient_name,
                'bill_id': bill_id,
                'total_amount': f"{total_amount:,.0f} VND",
                'due_date': due_date,
                'payment_methods': 'Tiền mặt, Thẻ tín dụng, Chuyển khoản ngân hàng'
            }
        )
        
        return await self.send_notification(message)
    
    async def send_payment_confirmation(self, patient_email: str, patient_name: str,
                                      bill_id: str, amount_paid: float, 
                                      payment_method: str) -> bool:
        """Send payment confirmation notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Xác nhận thanh toán thành công",
            content="",
            notification_type="payment_confirmation",
            channel="email",
            priority=NotificationPriority.MEDIUM,
            template_name="payment_confirmation",
            template_data={
                'patient_name': patient_name,
                'bill_id': bill_id,
                'amount_paid': f"{amount_paid:,.0f} VND",
                'payment_method': payment_method,
                'payment_date': datetime.now().strftime('%d/%m/%Y %H:%M')
            }
        )
        
        return await self.send_notification(message)
    
    # Medical Test Notifications
    async def send_test_results_ready(self, patient_email: str, patient_name: str,
                                    test_name: str, doctor_name: str) -> bool:
        """Send test results ready notification."""
        message = NotificationMessage(
            recipient=patient_email,
            subject="Kết quả xét nghiệm đã có",
            content="",
            notification_type="test_results_ready",
            channel="email",
            priority=NotificationPriority.HIGH,
            template_name="test_results_ready",
            template_data={
                'patient_name': patient_name,
                'test_name': test_name,
                'doctor_name': doctor_name,
                'hospital_name': 'Bệnh viện Đa khoa'
            }
        )
        
        return await self.send_notification(message)
    
    # Emergency Notifications
    async def send_emergency_alert(self, recipient_list: List[str], 
                                 alert_message: str, location: str) -> Dict[str, List[bool]]:
        """Send emergency alert to multiple recipients."""
        results = {'email': [], 'sms': []}
        
        for recipient in recipient_list:
            # Determine if recipient is email or phone
            if '@' in recipient:
                message = NotificationMessage(
                    recipient=recipient,
                    subject="KHẨN CẤP - Cảnh báo y tế",
                    content=f"CẢNH BÁO KHẨN CẤP: {alert_message}\nVị trí: {location}\nThời gian: {datetime.now().strftime('%d/%m/%Y %H:%M')}",
                    notification_type="emergency_alert",
                    channel="email",
                    priority=NotificationPriority.URGENT
                )
                success = await self.send_notification(message)
                results['email'].append(success)
            else:
                message = NotificationMessage(
                    recipient=recipient,
                    subject="KHẨN CẤP",
                    content=f"CẢNH BÁO: {alert_message} tại {location}. Thời gian: {datetime.now().strftime('%H:%M %d/%m')}",
                    notification_type="emergency_alert",
                    channel="sms",
                    priority=NotificationPriority.URGENT
                )
                success = await self.send_notification(message)
                results['sms'].append(success)
        
        return results


class NotificationTemplateManager:
    """
    Manages notification templates for different types of hospital notifications.
    Provides template registration and rendering capabilities.
    """
    
    def __init__(self):
        self.templates = {}
        self._register_default_templates()
    
    def _register_default_templates(self):
        """Register default notification templates."""
        self.templates.update({
            'appointment_confirmation': {
                'subject': 'Xác nhận lịch hẹn khám bệnh - {hospital_name}',
                'email_template': 'emails/appointment_confirmation.html',
                'sms_template': 'Xác nhận lịch hẹn: {patient_name} - {doctor_name} - {appointment_date} {appointment_time}'
            },
            'appointment_reminder': {
                'subject': 'Nhắc nhở lịch hẹn - {hospital_name}',
                'email_template': 'emails/appointment_reminder.html',
                'sms_template': 'Nhắc nhở: Lịch hẹn với BS {doctor_name} vào {appointment_time} ngày {appointment_date}'
            },
            'bill_notification': {
                'subject': 'Thông báo thanh toán viện phí - Hóa đơn #{bill_id}',
                'email_template': 'emails/bill_notification.html',
                'sms_template': 'Hóa đơn #{bill_id}: {total_amount} VND. Hạn thanh toán: {due_date}'
            }
        })
    
    def get_template(self, template_name: str, channel: str) -> Optional[str]:
        """Get template for specified notification type and channel."""
        template = self.templates.get(template_name)
        if template:
            return template.get(f'{channel}_template')
        return None
    
    def register_template(self, template_name: str, templates: Dict[str, str]):
        """Register custom notification template."""
        self.templates[template_name] = templates
