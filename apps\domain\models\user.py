"""
User Domain Model
Defines the User entity for authentication and authorization.
"""
from django.contrib.auth.models import AbstractBaseU<PERSON>, BaseUserManager, PermissionsMixin
from django.db import models
from django.utils import timezone
from apps.domain.shared.base_entity import BaseEntity


class UserManager(BaseUserManager):
    """Custom user manager for User model."""
    
    def create_user(self, ten_dang_nhap, mat_khau=None, **extra_fields):
        """Create and return a regular user."""
        if not ten_dang_nhap:
            raise ValueError('Tên đăng nhập là bắt buộc')
        
        user = self.model(ten_dang_nhap=ten_dang_nhap, **extra_fields)
        user.set_password(mat_khau)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, ten_dang_nhap, mat_khau=None, **extra_fields):
        """Create and return a superuser."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('vai_tro', 'Admin')
        extra_fields.setdefault('trang_thai', 'Active')
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser phải có is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser phải có is_superuser=True.')
        
        return self.create_user(ten_dang_nhap, mat_khau, **extra_fields)


class NguoiDung(AbstractBaseUser, PermissionsMixin, BaseEntity):
    """
    User model for hospital management system.
    Extends Django's AbstractBaseUser for custom authentication.
    """
    
    VAI_TRO_CHOICES = [
        ('Admin', 'Quản trị viên'),
        ('Doctor', 'Bác sĩ'),
        ('Nurse', 'Y tá'),
        ('Pharmacist', 'Dược sĩ'),
        ('Receptionist', 'Lễ tân'),
        ('Cashier', 'Thu ngân'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('Active', 'Hoạt động'),
        ('Inactive', 'Không hoạt động'),
        ('Suspended', 'Tạm khóa'),
    ]
    
    # Primary Key
    NguoiDungID = models.AutoField(primary_key=True)
    
    # Authentication fields
    ten_dang_nhap = models.CharField(
        'Tên đăng nhập',
        max_length=50,
        unique=True,
        help_text='Tên đăng nhập duy nhất'
    )
    
    # Personal information
    ho_ten = models.CharField(
        'Họ và tên',
        max_length=100,
        help_text='Họ và tên đầy đủ của người dùng'
    )
    
    email = models.EmailField(
        'Email',
        unique=True,
        help_text='Địa chỉ email duy nhất'
    )
    
    so_dien_thoai = models.CharField(
        'Số điện thoại',
        max_length=15,
        blank=True,
        null=True,
        help_text='Số điện thoại liên lạc'
    )
    
    # Role and permissions
    vai_tro = models.CharField(
        'Vai trò',
        max_length=20,
        choices=VAI_TRO_CHOICES,
        default='Receptionist',
        help_text='Vai trò của người dùng trong hệ thống'
    )
    
    # Status
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Active',
        help_text='Trạng thái hoạt động của tài khoản'
    )
    
    # Timestamps
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo tài khoản'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật lần cuối'
    )
    
    lan_dang_nhap_cuoi = models.DateTimeField(
        'Lần đăng nhập cuối',
        blank=True,
        null=True,
        help_text='Thời gian đăng nhập lần cuối'
    )
    
    # Django auth fields
    is_active = models.BooleanField(
        'Kích hoạt',
        default=True,
        help_text='Tài khoản có được kích hoạt hay không'
    )
    
    is_staff = models.BooleanField(
        'Là nhân viên',
        default=False,
        help_text='Có quyền truy cập admin hay không'
    )
    
    # Manager
    objects = UserManager()
    
    # Authentication settings
    USERNAME_FIELD = 'ten_dang_nhap'
    REQUIRED_FIELDS = ['ho_ten', 'email']
    
    class Meta:
        db_table = 'NguoiDung'
        verbose_name = 'Người dùng'
        verbose_name_plural = 'Người dùng'
        ordering = ['ho_ten']
        indexes = [
            models.Index(fields=['ten_dang_nhap']),
            models.Index(fields=['email']),
            models.Index(fields=['vai_tro']),
            models.Index(fields=['trang_thai']),
        ]
    
    def __str__(self):
        return f"{self.ho_ten} ({self.ten_dang_nhap})"
    
    def save(self, *args, **kwargs):
        """Override save to update timestamps."""
        self.ngay_cap_nhat = timezone.now()
        
        # Sync is_active with trang_thai
        self.is_active = self.trang_thai == 'Active'
        
        super().save(*args, **kwargs)
    
    # Business methods
    def is_doctor(self) -> bool:
        """Check if user is a doctor."""
        return self.vai_tro == 'Doctor'
    
    def is_nurse(self) -> bool:
        """Check if user is a nurse."""
        return self.vai_tro == 'Nurse'
    
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.vai_tro == 'Admin'
    
    def can_manage_patients(self) -> bool:
        """Check if user can manage patients."""
        return self.vai_tro in ['Doctor', 'Nurse', 'Receptionist', 'Admin']
    
    def can_prescribe_medication(self) -> bool:
        """Check if user can prescribe medication."""
        return self.vai_tro == 'Doctor'
    
    def can_dispense_medication(self) -> bool:
        """Check if user can dispense medication."""
        return self.vai_tro in ['Pharmacist', 'Doctor']
    
    def can_manage_billing(self) -> bool:
        """Check if user can manage billing."""
        return self.vai_tro in ['Cashier', 'Admin']
    
    def can_view_medical_records(self) -> bool:
        """Check if user can view medical records."""
        return self.vai_tro in ['Doctor', 'Nurse', 'Admin']
    
    def get_permissions(self) -> list:
        """Get user permissions based on role."""
        role_permissions = {
            'Admin': [
                'manage_users', 'manage_patients', 'manage_appointments',
                'manage_medications', 'manage_prescriptions', 'manage_billing',
                'view_reports', 'manage_departments', 'manage_rooms'
            ],
            'Doctor': [
                'manage_patients', 'manage_appointments', 'prescribe_medications',
                'view_medical_records', 'create_prescriptions'
            ],
            'Nurse': [
                'manage_patients', 'view_appointments', 'view_medical_records',
                'assist_prescriptions'
            ],
            'Pharmacist': [
                'manage_medications', 'dispense_medications', 'view_prescriptions'
            ],
            'Receptionist': [
                'manage_patients', 'manage_appointments', 'basic_billing'
            ],
            'Cashier': [
                'manage_billing', 'process_payments', 'view_bills'
            ]
        }
        
        return role_permissions.get(self.vai_tro, [])
    
    def update_last_login(self):
        """Update last login timestamp."""
        self.lan_dang_nhap_cuoi = timezone.now()
        self.save(update_fields=['lan_dang_nhap_cuoi'])
    
    def deactivate_account(self, reason: str = None):
        """Deactivate user account."""
        self.trang_thai = 'Inactive'
        self.is_active = False
        
        if reason:
            # Log deactivation reason (could be stored in audit log)
            pass
        
        self.save(update_fields=['trang_thai', 'is_active', 'ngay_cap_nhat'])
    
    def activate_account(self):
        """Activate user account."""
        self.trang_thai = 'Active'
        self.is_active = True
        self.save(update_fields=['trang_thai', 'is_active', 'ngay_cap_nhat'])
    
    def suspend_account(self, reason: str = None):
        """Suspend user account."""
        self.trang_thai = 'Suspended'
        self.is_active = False
        
        if reason:
            # Log suspension reason (could be stored in audit log)
            pass
        
        self.save(update_fields=['trang_thai', 'is_active', 'ngay_cap_nhat'])
    
    @property
    def display_role(self) -> str:
        """Get display name for role."""
        return dict(self.VAI_TRO_CHOICES).get(self.vai_tro, self.vai_tro)
    
    @property
    def display_status(self) -> str:
        """Get display name for status."""
        return dict(self.TRANG_THAI_CHOICES).get(self.trang_thai, self.trang_thai)
    
    @property
    def full_name(self) -> str:
        """Get full name."""
        return self.ho_ten
    
    @property
    def initials(self) -> str:
        """Get user initials."""
        name_parts = self.ho_ten.split()
        if len(name_parts) >= 2:
            return f"{name_parts[0][0]}{name_parts[-1][0]}".upper()
        elif len(name_parts) == 1:
            return name_parts[0][:2].upper()
        else:
            return self.ten_dang_nhap[:2].upper()
    
    def clean(self):
        """Validate model fields."""
        from django.core.exceptions import ValidationError
        
        # Validate email format
        if self.email and '@' not in self.email:
            raise ValidationError({'email': 'Email không hợp lệ'})
        
        # Validate phone number
        if self.so_dien_thoai:
            import re
            phone_pattern = r'^[\d\-\+\(\)\s]+$'
            if not re.match(phone_pattern, self.so_dien_thoai):
                raise ValidationError({'so_dien_thoai': 'Số điện thoại không hợp lệ'})
    
    # Domain Events
    def raise_user_created_event(self):
        """Raise user created domain event."""
        from apps.domain.events.domain_events import UserCreatedEvent
        event = UserCreatedEvent(
            user_id=self.NguoiDungID,
            username=self.ten_dang_nhap,
            full_name=self.ho_ten,
            email=self.email,
            role=self.vai_tro,
            created_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_user_role_changed_event(self, old_role: str, new_role: str):
        """Raise user role changed domain event."""
        from apps.domain.events.domain_events import UserRoleChangedEvent
        event = UserRoleChangedEvent(
            user_id=self.NguoiDungID,
            username=self.ten_dang_nhap,
            old_role=old_role,
            new_role=new_role,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_user_status_changed_event(self, old_status: str, new_status: str):
        """Raise user status changed domain event."""
        from apps.domain.events.domain_events import UserStatusChangedEvent
        event = UserStatusChangedEvent(
            user_id=self.NguoiDungID,
            username=self.ten_dang_nhap,
            old_status=old_status,
            new_status=new_status,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
