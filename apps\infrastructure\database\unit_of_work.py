"""
Unit of Work Pattern Implementation
Manages database transactions and coordinates repository work across multiple entities.
"""
from typing import Dict, Any, List
from django.db import transaction
from django.db.models import Model
from apps.domain.shared.interfaces import IUnitOfWork
from apps.infrastructure.database.repositories import (
    PatientRepository, MedicalStaffRepository, AppointmentRepository,
    MedicationRepository, PrescriptionRepository, RoomRepository,
    BillRepository, UserRepository, DepartmentRepository
)
import logging


logger = logging.getLogger('hospital_management.database')


class UnitOfWork(IUnitOfWork):
    """
    Unit of Work implementation for managing database transactions.
    Coordinates work across multiple repositories and ensures data consistency.
    """
    
    def __init__(self):
        # Repository instances
        self._patient_repo = None
        self._medical_staff_repo = None
        self._appointment_repo = None
        self._medication_repo = None
        self._prescription_repo = None
        self._room_repo = None
        self._bill_repo = None
        self._user_repo = None
        self._department_repo = None
        
        # Transaction state
        self._transaction = None
        self._is_committed = False
        self._tracked_entities = []
        self._new_entities = []
        self._modified_entities = []
        self._deleted_entities = []
    
    # Repository Properties
    @property
    def patients(self) -> PatientRepository:
        """Get patient repository."""
        if self._patient_repo is None:
            self._patient_repo = PatientRepository()
        return self._patient_repo
    
    @property
    def medical_staff(self) -> MedicalStaffRepository:
        """Get medical staff repository."""
        if self._medical_staff_repo is None:
            self._medical_staff_repo = MedicalStaffRepository()
        return self._medical_staff_repo
    
    @property
    def appointments(self) -> AppointmentRepository:
        """Get appointment repository."""
        if self._appointment_repo is None:
            self._appointment_repo = AppointmentRepository()
        return self._appointment_repo
    
    @property
    def medications(self) -> MedicationRepository:
        """Get medication repository."""
        if self._medication_repo is None:
            self._medication_repo = MedicationRepository()
        return self._medication_repo
    
    @property
    def prescriptions(self) -> PrescriptionRepository:
        """Get prescription repository."""
        if self._prescription_repo is None:
            self._prescription_repo = PrescriptionRepository()
        return self._prescription_repo
    
    @property
    def rooms(self) -> RoomRepository:
        """Get room repository."""
        if self._room_repo is None:
            self._room_repo = RoomRepository()
        return self._room_repo
    
    @property
    def bills(self) -> BillRepository:
        """Get bill repository."""
        if self._bill_repo is None:
            self._bill_repo = BillRepository()
        return self._bill_repo
    
    @property
    def users(self) -> UserRepository:
        """Get user repository."""
        if self._user_repo is None:
            self._user_repo = UserRepository()
        return self._user_repo
    
    @property
    def departments(self) -> DepartmentRepository:
        """Get department repository."""
        if self._department_repo is None:
            self._department_repo = DepartmentRepository()
        return self._department_repo
    
    # Transaction Management
    async def begin_transaction(self) -> None:
        """Begin database transaction."""
        if self._transaction is not None:
            raise RuntimeError("Transaction already started")
        
        self._transaction = transaction.atomic()
        await self._transaction.__aenter__()
        self._is_committed = False
        
        logger.debug("Database transaction started")
    
    async def commit(self) -> None:
        """Commit the current transaction."""
        if self._transaction is None:
            raise RuntimeError("No transaction to commit")
        
        try:
            # Save all tracked entities
            await self._save_tracked_entities()
            
            # Commit the transaction
            await self._transaction.__aexit__(None, None, None)
            self._is_committed = True
            
            logger.debug(
                f"Transaction committed successfully. "
                f"Entities: {len(self._new_entities)} new, "
                f"{len(self._modified_entities)} modified, "
                f"{len(self._deleted_entities)} deleted"
            )
            
        except Exception as e:
            await self.rollback()
            logger.error(f"Transaction commit failed: {str(e)}")
            raise
        finally:
            self._clear_tracking()
    
    async def rollback(self) -> None:
        """Rollback the current transaction."""
        if self._transaction is None:
            return
        
        try:
            await self._transaction.__aexit__(Exception, Exception("Rollback"), None)
            logger.debug("Transaction rolled back")
        except Exception as e:
            logger.error(f"Transaction rollback failed: {str(e)}")
        finally:
            self._transaction = None
            self._is_committed = False
            self._clear_tracking()
    
    async def save_changes(self) -> None:
        """Save all tracked changes within current transaction."""
        if self._transaction is None:
            # Auto-start transaction if not already started
            await self.begin_transaction()
        
        await self._save_tracked_entities()
    
    # Entity Tracking
    def register_new(self, entity: Model) -> None:
        """Register a new entity for insertion."""
        if entity not in self._tracked_entities:
            self._tracked_entities.append(entity)
            self._new_entities.append(entity)
            
            logger.debug(f"Registered new entity: {entity.__class__.__name__}")
    
    def register_modified(self, entity: Model) -> None:
        """Register an entity as modified."""
        if entity not in self._tracked_entities:
            self._tracked_entities.append(entity)
        
        if entity not in self._modified_entities and entity not in self._new_entities:
            self._modified_entities.append(entity)
            
            logger.debug(f"Registered modified entity: {entity.__class__.__name__}")
    
    def register_deleted(self, entity: Model) -> None:
        """Register an entity for deletion."""
        if entity in self._new_entities:
            # Remove from new entities if it was just added
            self._new_entities.remove(entity)
        elif entity in self._modified_entities:
            # Remove from modified entities if it was modified
            self._modified_entities.remove(entity)
        
        if entity not in self._deleted_entities:
            self._deleted_entities.append(entity)
            
            logger.debug(f"Registered deleted entity: {entity.__class__.__name__}")
        
        if entity in self._tracked_entities:
            self._tracked_entities.remove(entity)
    
    # Private Methods
    async def _save_tracked_entities(self) -> None:
        """Save all tracked entities to database."""
        # Save new entities first
        for entity in self._new_entities:
            await entity.asave()
            logger.debug(f"Saved new entity: {entity.__class__.__name__} ID: {entity.pk}")
        
        # Update modified entities
        for entity in self._modified_entities:
            await entity.asave()
            logger.debug(f"Updated entity: {entity.__class__.__name__} ID: {entity.pk}")
        
        # Delete entities
        for entity in self._deleted_entities:
            await entity.adelete()
            logger.debug(f"Deleted entity: {entity.__class__.__name__} ID: {entity.pk}")
    
    def _clear_tracking(self) -> None:
        """Clear all entity tracking."""
        self._tracked_entities.clear()
        self._new_entities.clear()
        self._modified_entities.clear()
        self._deleted_entities.clear()
        self._transaction = None
    
    # Context Manager Support
    async def __aenter__(self):
        """Enter async context manager."""
        await self.begin_transaction()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context manager."""
        if exc_type is None:
            await self.commit()
        else:
            await self.rollback()
    
    # Utility Methods
    def is_in_transaction(self) -> bool:
        """Check if currently in transaction."""
        return self._transaction is not None
    
    def get_tracked_entities_count(self) -> Dict[str, int]:
        """Get count of tracked entities by type."""
        return {
            'total': len(self._tracked_entities),
            'new': len(self._new_entities),
            'modified': len(self._modified_entities),
            'deleted': len(self._deleted_entities)
        }
    
    async def execute_in_transaction(self, func, *args, **kwargs) -> Any:
        """Execute function within transaction."""
        async with self:
            return await func(*args, **kwargs)


class UnitOfWorkFactory:
    """
    Factory for creating Unit of Work instances.
    Provides dependency injection and configuration support.
    """
    
    @staticmethod
    def create() -> UnitOfWork:
        """Create new Unit of Work instance."""
        return UnitOfWork()
    
    @staticmethod
    async def create_with_transaction() -> UnitOfWork:
        """Create Unit of Work instance with auto-started transaction."""
        uow = UnitOfWork()
        await uow.begin_transaction()
        return uow
