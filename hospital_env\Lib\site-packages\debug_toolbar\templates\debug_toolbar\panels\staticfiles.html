{% load i18n %}

<h4>{% blocktrans count staticfiles_dirs|length as dirs_count %}Static file path{% plural %}Static file paths{% endblocktrans %}</h4>
{% if staticfiles_dirs %}
  <ol>
    {% for prefix, staticfiles_dir in staticfiles_dirs %}
      <li>{{ staticfiles_dir }}{% if prefix %} {% blocktrans %}(prefix {{ prefix }}){% endblocktrans %}{% endif %}</li>
    {% endfor %}
  </ol>
{% else %}
  <p>{% trans "None" %}</p>
{% endif %}

<h4>{% blocktrans count staticfiles_apps|length as apps_count %}Static file app{% plural %}Static file apps{% endblocktrans %}</h4>
{% if staticfiles_apps %}
  <ol>
    {% for static_app in staticfiles_apps %}
      <li>{{ static_app }}</li>
    {% endfor %}
  </ol>
{% else %}
  <p>{% trans "None" %}</p>
{% endif %}

<h4>{% blocktrans count staticfiles|length as staticfiles_count %}Static file{% plural %}Static files{% endblocktrans %}</h4>
{% if staticfiles %}
  <dl>
    {% for staticfile in staticfiles %}
      <dt><strong><a class="toggleTemplate" href="{{ staticfile.url }}">{{ staticfile }}</a></strong></dt>
      <dd><samp>{{ staticfile.real_path }}</samp></dd>
    {% endfor %}
  </dl>
{% else %}
  <p>{% trans "None" %}</p>
{% endif %}


{% for finder, payload in staticfiles_finders.items %}
  <h4>{{ finder }} ({% blocktrans count payload|length as payload_count %}{{ payload_count }} file{% plural %}{{ payload_count }} files{% endblocktrans %})</h4>
  <table>
    <thead>
      <tr>
        <th>{% trans 'Path' %}</th>
        <th>{% trans 'Location' %}</th>
      </tr>
    </thead>
    <tbody>
      {% for path, real_path in payload %}
        <tr>
          <td>{{ path }}</td>
          <td>{{ real_path }}</td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% endfor %}
