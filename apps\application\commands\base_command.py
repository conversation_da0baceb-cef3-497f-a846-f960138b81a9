"""
Command Pattern Implementation for CQRS
Defines base classes for commands and command handlers following CQRS pattern.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, TypeVar
from datetime import datetime
from dataclasses import dataclass


TCommand = TypeVar('TCommand')
TResult = TypeVar('TResult')


@dataclass
class CommandResult:
    """Standard command execution result."""
    success: bool
    message: str
    data: Any = None
    errors: Dict[str, Any] = None
    
    @classmethod
    def success_result(cls, data: Any = None, message: str = "Operation completed successfully"):
        """Create success result."""
        return cls(success=True, message=message, data=data)
    
    @classmethod
    def failure_result(cls, message: str, errors: Dict[str, Any] = None):
        """Create failure result."""
        return cls(success=False, message=message, errors=errors)


class ICommand(ABC):
    """Base interface for all commands."""
    
    def __init__(self):
        self.command_id = datetime.now().isoformat()
        self.executed_at = None


class ICommandHandler(ABC, Generic[TCommand, TResult]):
    """Base interface for command handlers."""
    
    @abstractmethod
    async def handle(self, command: TCommand) -> TResult:
        """Handle the command and return result."""
        pass
    
    @abstractmethod
    def can_handle(self, command: ICommand) -> bool:
        """Check if this handler can handle the given command."""
        pass


# Patient Commands
@dataclass
class CreatePatientCommand(ICommand):
    """Command to create a new patient."""
    ho_ten: str
    ngay_sinh: str
    gioi_tinh: str
    cmnd: str
    dia_chi: str
    so_dien_thoai: str
    email: str
    nhom_mau: str
    tien_su_benh: str = ""
    di_ung: str = ""
    bao_hiem_yte: str = ""


@dataclass
class UpdatePatientCommand(ICommand):
    """Command to update patient information."""
    patient_id: int
    update_data: Dict[str, Any]


# Appointment Commands
@dataclass
class ScheduleAppointmentCommand(ICommand):
    """Command to schedule a new appointment."""
    ma_benh_nhan: int
    ma_bac_si: int
    ngay_hen: str
    gio_hen: str
    ly_do_kham: str
    trieu_chung: str = ""
    ghi_chu: str = ""


@dataclass
class CancelAppointmentCommand(ICommand):
    """Command to cancel an appointment."""
    appointment_id: int
    reason: str
    cancelled_by: int


@dataclass
class RescheduleAppointmentCommand(ICommand):
    """Command to reschedule an appointment."""
    appointment_id: int
    new_date: str
    new_time: str
    reason: str


# Prescription Commands
@dataclass
class CreatePrescriptionCommand(ICommand):
    """Command to create a new prescription."""
    ma_lich_hen: int
    ma_bac_si: int
    ma_benh_nhan: int
    chan_doan: str
    loi_dan: str
    ngay_tai_kham: str = None
    medications: list = None


@dataclass
class DispensePrescriptionCommand(ICommand):
    """Command to dispense a prescription."""
    prescription_id: int
    dispensed_by: int
    notes: str = ""


# Billing Commands
@dataclass
class CreateBillCommand(ICommand):
    """Command to create a new bill."""
    ma_benh_nhan: int
    tien_kham: float
    tien_thuoc: float
    tien_phong: float
    tien_dich_vu: float
    nguoi_lap: int
    ghi_chu: str = ""


@dataclass
class ProcessPaymentCommand(ICommand):
    """Command to process payment for a bill."""
    bill_id: int
    payment_method: str
    payment_data: Dict[str, Any]
    processed_by: int


# Room Commands
@dataclass
class AssignRoomCommand(ICommand):
    """Command to assign a room to a patient."""
    patient_id: int
    room_id: int
    start_date: str
    assigned_by: int


@dataclass
class ReleaseRoomCommand(ICommand):
    """Command to release a room."""
    room_id: int
    patient_id: int
    release_date: str
    released_by: int


# Medication Commands
@dataclass
class UpdateMedicationStockCommand(ICommand):
    """Command to update medication stock."""
    medication_id: int
    quantity_change: int
    operation_type: str  # 'add', 'subtract', 'set'
    updated_by: int
    notes: str = ""


@dataclass
class CreateMedicationCommand(ICommand):
    """Command to add new medication."""
    ten_thuoc: str
    loai_thuoc: str
    don_vi_tinh: str
    gia_nhap: float
    gia_ban: float
    ngay_san_xuat: str
    ngay_het_han: str
    nha_san_xuat: str
    mo_ta: str = ""


# User Commands
@dataclass
class CreateUserCommand(ICommand):
    """Command to create a new user."""
    ten_dang_nhap: str
    mat_khau: str
    ho_ten: str
    email: str
    so_dien_thoai: str
    vai_tro: str


@dataclass
class UpdateUserCommand(ICommand):
    """Command to update user information."""
    user_id: int
    update_data: Dict[str, Any]


@dataclass
class ChangePasswordCommand(ICommand):
    """Command to change user password."""
    user_id: int
    old_password: str
    new_password: str
