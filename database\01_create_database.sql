-- Hospital Management System Database Schema
-- Part 1: Database and Tables Creation
-- Database: QuanLyBenhVien
-- SQL Server Configuration: SA account, password: 123

-- Drop database if exists and recreate
IF EXISTS (SELECT name FROM sys.databases WHERE name = N'QuanLyBenhVien')
BEGIN
    ALTER DATABASE QuanLyBenhVien SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE QuanLyBenhVien;
END
GO

CREATE DATABASE QuanLyBenhVien
GO

USE QuanLyBenhVien
GO

-- =============================================
-- Table 1: NguoiDung (Users)
-- =============================================
CREATE TABLE NguoiDung (
    MaNguoiDung INT IDENTITY(1,1) PRIMARY KEY,
    TenDangNhap NVARCHAR(50) UNIQUE NOT NULL,
    MatKhau NVARCHAR(255) NOT NULL,
    HoTen NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100) UNIQUE NOT NULL,
    SoDienThoai NVARCHAR(15),
    VaiTro NVARCHAR(20) CHECK (VaiTro IN (N'Admin', N'Bác sĩ', N'Y tá', N'Lễ tân', N'Kế toán')),
    TrangThai BIT DEFAULT 1,
    NgayTao DATETIME DEFAULT GETDATE(),
    NgayCapNhat DATETIME DEFAULT GETDATE()
);

-- =============================================
-- Table 2: KhoaPhong (Departments)
-- =============================================
CREATE TABLE KhoaPhong (
    MaKhoa INT IDENTITY(1,1) PRIMARY KEY,
    TenKhoa NVARCHAR(100) NOT NULL,
    LoaiKhoa NVARCHAR(50),
    MaTruongKhoa INT,
    SoDienThoai NVARCHAR(15),
    ViTri NVARCHAR(100),
    MoTa NVARCHAR(500),
    NgayTao DATETIME DEFAULT GETDATE()
);

-- =============================================
-- Table 3: NhanVienYTe (Medical Staff - Doctors & Nurses)
-- =============================================
CREATE TABLE NhanVienYTe (
    MaNhanVien INT IDENTITY(1,1) PRIMARY KEY,
    MaNguoiDung INT FOREIGN KEY REFERENCES NguoiDung(MaNguoiDung),
    MaKhoa INT FOREIGN KEY REFERENCES KhoaPhong(MaKhoa),
    LoaiNhanVien NVARCHAR(20) CHECK (LoaiNhanVien IN (N'Bác sĩ', N'Y tá')),
    ChuyenKhoa NVARCHAR(100),
    BangCap NVARCHAR(100),
    KinhNghiem INT,
    GioLamViec NVARCHAR(200),
    LuongCoBan DECIMAL(18,0),
    NgayVaoLam DATE,
    TrangThai NVARCHAR(20) DEFAULT N'Đang làm việc'
);

-- =============================================
-- Table 4: BenhNhan (Patients)
-- =============================================
CREATE TABLE BenhNhan (
    MaBenhNhan INT IDENTITY(1,1) PRIMARY KEY,
    HoTen NVARCHAR(100) NOT NULL,
    NgaySinh DATE NOT NULL,
    GioiTinh NVARCHAR(10) CHECK (GioiTinh IN (N'Nam', N'Nữ', N'Khác')),
    CMND NVARCHAR(20) UNIQUE,
    DiaChi NVARCHAR(200),
    SoDienThoai NVARCHAR(15),
    Email NVARCHAR(100),
    NhomMau NVARCHAR(10),
    TienSuBenh NVARCHAR(500),
    DiUng NVARCHAR(200),
    BaoHiemYTe NVARCHAR(50),
    NgayDangKy DATETIME DEFAULT GETDATE(),
    TrangThai NVARCHAR(20) DEFAULT N'Hoạt động'
);

-- =============================================
-- Table 5: PhongBenh (Rooms)
-- =============================================
CREATE TABLE PhongBenh (
    MaPhong INT IDENTITY(1,1) PRIMARY KEY,
    TenPhong NVARCHAR(50) NOT NULL,
    MaKhoa INT FOREIGN KEY REFERENCES KhoaPhong(MaKhoa),
    LoaiPhong NVARCHAR(20) CHECK (LoaiPhong IN (N'VIP', N'Thường', N'ICU', N'Cấp cứu')),
    SoGiuong INT DEFAULT 1,
    SoGiuongTrong INT,
    GiaTheoNgay DECIMAL(18,0) NOT NULL,
    TrangThai NVARCHAR(20) DEFAULT N'Sẵn sàng',
    TienNghi NVARCHAR(200)
);

-- =============================================
-- Table 6: LichHen (Appointments)
-- =============================================
CREATE TABLE LichHen (
    MaLichHen INT IDENTITY(1,1) PRIMARY KEY,
    MaBenhNhan INT FOREIGN KEY REFERENCES BenhNhan(MaBenhNhan),
    MaBacSi INT FOREIGN KEY REFERENCES NhanVienYTe(MaNhanVien),
    NgayHen DATE NOT NULL,
    GioHen TIME NOT NULL,
    LyDoKham NVARCHAR(200),
    TrieuChung NVARCHAR(500),
    TrangThai NVARCHAR(20) DEFAULT N'Đã đặt',
    GhiChu NVARCHAR(500),
    NgayTao DATETIME DEFAULT GETDATE(),
    CONSTRAINT CHK_TrangThaiLichHen CHECK (TrangThai IN (N'Đã đặt', N'Đã khám', N'Hủy', N'Không đến'))
);

-- =============================================
-- Table 7: Thuoc (Medications)
-- =============================================
CREATE TABLE Thuoc (
    MaThuoc INT IDENTITY(1,1) PRIMARY KEY,
    TenThuoc NVARCHAR(100) NOT NULL,
    LoaiThuoc NVARCHAR(50),
    DonViTinh NVARCHAR(20),
    SoLuongTon INT DEFAULT 0,
    GiaNhap DECIMAL(18,0),
    GiaBan DECIMAL(18,0),
    NgaySanXuat DATE,
    NgayHetHan DATE,
    NhaSanXuat NVARCHAR(100),
    MoTa NVARCHAR(500),
    CanhBao INT DEFAULT 10,
    TrangThai NVARCHAR(20) DEFAULT N'Còn hàng'
);

-- =============================================
-- Table 8: DonThuoc (Prescriptions)
-- =============================================
CREATE TABLE DonThuoc (
    MaDonThuoc INT IDENTITY(1,1) PRIMARY KEY,
    MaLichHen INT FOREIGN KEY REFERENCES LichHen(MaLichHen),
    MaBacSi INT FOREIGN KEY REFERENCES NhanVienYTe(MaNhanVien),
    MaBenhNhan INT FOREIGN KEY REFERENCES BenhNhan(MaBenhNhan),
    NgayKeDon DATETIME DEFAULT GETDATE(),
    ChanDoan NVARCHAR(500),
    LoiDan NVARCHAR(500),
    NgayTaiKham DATE,
    TongTien DECIMAL(18,0) DEFAULT 0,
    TrangThai NVARCHAR(20) DEFAULT N'Mới tạo'
);

-- =============================================
-- Table 9: ChiTietDonThuoc (Prescription Details)
-- =============================================
CREATE TABLE ChiTietDonThuoc (
    MaChiTiet INT IDENTITY(1,1) PRIMARY KEY,
    MaDonThuoc INT FOREIGN KEY REFERENCES DonThuoc(MaDonThuoc),
    MaThuoc INT FOREIGN KEY REFERENCES Thuoc(MaThuoc),
    SoLuong INT NOT NULL,
    LieuDung NVARCHAR(100),
    CachDung NVARCHAR(200),
    SoNgay INT,
    DonGia DECIMAL(18,0),
    ThanhTien DECIMAL(18,0)
);

-- =============================================
-- Table 10: HoaDon (Bills)
-- =============================================
CREATE TABLE HoaDon (
    MaHoaDon INT IDENTITY(1,1) PRIMARY KEY,
    MaBenhNhan INT FOREIGN KEY REFERENCES BenhNhan(MaBenhNhan),
    NgayLap DATETIME DEFAULT GETDATE(),
    TienKham DECIMAL(18,0) DEFAULT 0,
    TienThuoc DECIMAL(18,0) DEFAULT 0,
    TienPhong DECIMAL(18,0) DEFAULT 0,
    TienDichVu DECIMAL(18,0) DEFAULT 0,
    TongTien DECIMAL(18,0) DEFAULT 0,
    BaoHiemChiTra DECIMAL(18,0) DEFAULT 0,
    BenhNhanTra DECIMAL(18,0) DEFAULT 0,
    PhuongThucThanhToan NVARCHAR(50),
    TrangThai NVARCHAR(20) DEFAULT N'Chưa thanh toán',
    GhiChu NVARCHAR(500),
    NguoiLap INT FOREIGN KEY REFERENCES NguoiDung(MaNguoiDung),
    CONSTRAINT CHK_TrangThaiHoaDon CHECK (TrangThai IN (N'Chưa thanh toán', N'Đã thanh toán', N'Hủy'))
);

-- Add Foreign Key for Department Head
ALTER TABLE KhoaPhong
ADD CONSTRAINT FK_TruongKhoa FOREIGN KEY (MaTruongKhoa) REFERENCES NhanVienYTe(MaNhanVien);

-- Create Indexes for Performance
CREATE INDEX IX_BenhNhan_CMND ON BenhNhan(CMND);
CREATE INDEX IX_BenhNhan_SoDienThoai ON BenhNhan(SoDienThoai);
CREATE INDEX IX_LichHen_NgayHen ON LichHen(NgayHen, GioHen);
CREATE INDEX IX_LichHen_MaBenhNhan ON LichHen(MaBenhNhan);
CREATE INDEX IX_LichHen_MaBacSi ON LichHen(MaBacSi);
CREATE INDEX IX_Thuoc_NgayHetHan ON Thuoc(NgayHetHan);
CREATE INDEX IX_HoaDon_MaBenhNhan ON HoaDon(MaBenhNhan);
CREATE INDEX IX_HoaDon_TrangThai ON HoaDon(TrangThai);
