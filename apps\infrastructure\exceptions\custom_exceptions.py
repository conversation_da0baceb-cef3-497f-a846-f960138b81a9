"""
Custom Exception Classes and Exception Handler
Defines domain-specific exceptions and provides centralized exception handling.
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.http import Http404
from django.core.exceptions import ValidationError, PermissionDenied
import logging


logger = logging.getLogger('hospital_management.exceptions')


# Domain Exception Base Classes
class HospitalManagementException(Exception):
    """Base exception class for hospital management domain."""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(HospitalManagementException):
    """Exception for validation errors."""
    pass


class BusinessRuleException(HospitalManagementException):
    """Exception for business rule violations."""
    pass


class AuthorizationException(HospitalManagementException):
    """Exception for authorization failures."""
    pass


class ResourceNotFoundException(HospitalManagementException):
    """Exception for resource not found errors."""
    pass


class ConflictException(HospitalManagementException):
    """Exception for resource conflicts."""
    pass


# Patient Domain Exceptions
class PatientNotFoundException(ResourceNotFoundException):
    """Exception when patient is not found."""
    
    def __init__(self, patient_id: int):
        super().__init__(
            message=f"Không tìm thấy bệnh nhân với ID: {patient_id}",
            error_code="PATIENT_NOT_FOUND",
            details={"patient_id": patient_id}
        )


class PatientAlreadyExistsException(ConflictException):
    """Exception when patient already exists."""
    
    def __init__(self, identifier: str, identifier_type: str = "CMND"):
        super().__init__(
            message=f"Bệnh nhân đã tồn tại với {identifier_type}: {identifier}",
            error_code="PATIENT_ALREADY_EXISTS",
            details={"identifier": identifier, "identifier_type": identifier_type}
        )


# Appointment Domain Exceptions
class AppointmentNotFoundException(ResourceNotFoundException):
    """Exception when appointment is not found."""
    
    def __init__(self, appointment_id: int):
        super().__init__(
            message=f"Không tìm thấy lịch hẹn với ID: {appointment_id}",
            error_code="APPOINTMENT_NOT_FOUND",
            details={"appointment_id": appointment_id}
        )


class AppointmentConflictException(ConflictException):
    """Exception when appointment conflicts with existing one."""
    
    def __init__(self, doctor_id: int, appointment_time: str):
        super().__init__(
            message=f"Bác sĩ đã có lịch hẹn vào thời gian {appointment_time}",
            error_code="APPOINTMENT_CONFLICT",
            details={"doctor_id": doctor_id, "appointment_time": appointment_time}
        )


class AppointmentCancellationException(BusinessRuleException):
    """Exception when appointment cannot be cancelled."""
    
    def __init__(self, appointment_id: int, reason: str):
        super().__init__(
            message=f"Không thể hủy lịch hẹn: {reason}",
            error_code="APPOINTMENT_CANCELLATION_FAILED",
            details={"appointment_id": appointment_id, "reason": reason}
        )


# Medical Staff Domain Exceptions
class MedicalStaffNotFoundException(ResourceNotFoundException):
    """Exception when medical staff is not found."""
    
    def __init__(self, staff_id: int):
        super().__init__(
            message=f"Không tìm thấy nhân viên y tế với ID: {staff_id}",
            error_code="MEDICAL_STAFF_NOT_FOUND",
            details={"staff_id": staff_id}
        )


class DoctorNotAvailableException(BusinessRuleException):
    """Exception when doctor is not available."""
    
    def __init__(self, doctor_id: int, requested_time: str):
        super().__init__(
            message=f"Bác sĩ không có lịch vào thời gian {requested_time}",
            error_code="DOCTOR_NOT_AVAILABLE",
            details={"doctor_id": doctor_id, "requested_time": requested_time}
        )


# Medication Domain Exceptions
class MedicationNotFoundException(ResourceNotFoundException):
    """Exception when medication is not found."""
    
    def __init__(self, medication_id: int):
        super().__init__(
            message=f"Không tìm thấy thuốc với ID: {medication_id}",
            error_code="MEDICATION_NOT_FOUND",
            details={"medication_id": medication_id}
        )


class InsufficientStockException(BusinessRuleException):
    """Exception when medication stock is insufficient."""
    
    def __init__(self, medication_name: str, requested: int, available: int):
        super().__init__(
            message=f"Không đủ thuốc {medication_name}. Yêu cầu: {requested}, Có sẵn: {available}",
            error_code="INSUFFICIENT_STOCK",
            details={"medication_name": medication_name, "requested": requested, "available": available}
        )


class ExpiredMedicationException(BusinessRuleException):
    """Exception when trying to use expired medication."""
    
    def __init__(self, medication_name: str, expiry_date: str):
        super().__init__(
            message=f"Thuốc {medication_name} đã hết hạn vào {expiry_date}",
            error_code="EXPIRED_MEDICATION",
            details={"medication_name": medication_name, "expiry_date": expiry_date}
        )


# Prescription Domain Exceptions
class PrescriptionNotFoundException(ResourceNotFoundException):
    """Exception when prescription is not found."""
    
    def __init__(self, prescription_id: int):
        super().__init__(
            message=f"Không tìm thấy đơn thuốc với ID: {prescription_id}",
            error_code="PRESCRIPTION_NOT_FOUND",
            details={"prescription_id": prescription_id}
        )


class PrescriptionValidationException(ValidationException):
    """Exception for prescription validation errors."""
    
    def __init__(self, errors: list):
        error_message = "Đơn thuốc không hợp lệ: " + "; ".join(errors)
        super().__init__(
            message=error_message,
            error_code="PRESCRIPTION_VALIDATION_FAILED",
            details={"validation_errors": errors}
        )


# Room Domain Exceptions
class RoomNotFoundException(ResourceNotFoundException):
    """Exception when room is not found."""
    
    def __init__(self, room_id: int):
        super().__init__(
            message=f"Không tìm thấy phòng với ID: {room_id}",
            error_code="ROOM_NOT_FOUND",
            details={"room_id": room_id}
        )


class RoomNotAvailableException(BusinessRuleException):
    """Exception when room is not available."""
    
    def __init__(self, room_id: int, reason: str = "Phòng đã được sử dụng"):
        super().__init__(
            message=f"Phòng {room_id} không khả dụng: {reason}",
            error_code="ROOM_NOT_AVAILABLE",
            details={"room_id": room_id, "reason": reason}
        )


# Billing Domain Exceptions
class BillNotFoundException(ResourceNotFoundException):
    """Exception when bill is not found."""
    
    def __init__(self, bill_id: int):
        super().__init__(
            message=f"Không tìm thấy hóa đơn với ID: {bill_id}",
            error_code="BILL_NOT_FOUND",
            details={"bill_id": bill_id}
        )


class PaymentProcessingException(BusinessRuleException):
    """Exception when payment processing fails."""
    
    def __init__(self, reason: str, transaction_id: str = None):
        super().__init__(
            message=f"Xử lý thanh toán thất bại: {reason}",
            error_code="PAYMENT_PROCESSING_FAILED",
            details={"reason": reason, "transaction_id": transaction_id}
        )


class InsufficientPaymentException(BusinessRuleException):
    """Exception when payment amount is insufficient."""
    
    def __init__(self, required_amount: float, provided_amount: float):
        super().__init__(
            message=f"Số tiền thanh toán không đủ. Yêu cầu: {required_amount:,.0f} VND, Nhận được: {provided_amount:,.0f} VND",
            error_code="INSUFFICIENT_PAYMENT",
            details={"required_amount": required_amount, "provided_amount": provided_amount}
        )


# Authentication and Authorization Exceptions
class InvalidCredentialsException(AuthorizationException):
    """Exception for invalid login credentials."""
    
    def __init__(self):
        super().__init__(
            message="Tên đăng nhập hoặc mật khẩu không chính xác",
            error_code="INVALID_CREDENTIALS"
        )


class AccessDeniedException(AuthorizationException):
    """Exception for access denied errors."""
    
    def __init__(self, resource: str, action: str):
        super().__init__(
            message=f"Không có quyền {action} trên {resource}",
            error_code="ACCESS_DENIED",
            details={"resource": resource, "action": action}
        )


# Custom Exception Handler
def custom_exception_handler(exc, context):
    """
    Custom exception handler for Django REST Framework.
    Provides consistent error response format across the application.
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Log the exception
    request = context.get('request')
    correlation_id = getattr(request, 'correlation_id', 'unknown') if request else 'unknown'
    
    logger.error(
        f"Exception occurred: {exc.__class__.__name__}",
        extra={
            'correlation_id': correlation_id,
            'exception_type': exc.__class__.__name__,
            'exception_message': str(exc),
            'request_path': request.path if request else 'unknown',
            'request_method': request.method if request else 'unknown',
            'user': getattr(request.user, 'username', 'anonymous') if request and hasattr(request, 'user') else 'anonymous'
        },
        exc_info=True
    )
    
    # Handle our custom exceptions
    if isinstance(exc, HospitalManagementException):
        custom_response_data = {
            'success': False,
            'message': exc.message,
            'error': {
                'code': exc.error_code,
                'details': exc.details
            },
            'data': None
        }
        
        # Determine HTTP status code based on exception type
        if isinstance(exc, ResourceNotFoundException):
            status_code = status.HTTP_404_NOT_FOUND
        elif isinstance(exc, ValidationException):
            status_code = status.HTTP_400_BAD_REQUEST
        elif isinstance(exc, AuthorizationException):
            status_code = status.HTTP_403_FORBIDDEN
        elif isinstance(exc, ConflictException):
            status_code = status.HTTP_409_CONFLICT
        elif isinstance(exc, BusinessRuleException):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        else:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        
        return Response(custom_response_data, status=status_code)
    
    # Handle Django built-in exceptions
    if isinstance(exc, Http404):
        custom_response_data = {
            'success': False,
            'message': 'Không tìm thấy tài nguyên',
            'error': {
                'code': 'RESOURCE_NOT_FOUND',
                'details': {}
            },
            'data': None
        }
        return Response(custom_response_data, status=status.HTTP_404_NOT_FOUND)
    
    if isinstance(exc, PermissionDenied):
        custom_response_data = {
            'success': False,
            'message': 'Không có quyền truy cập',
            'error': {
                'code': 'PERMISSION_DENIED',
                'details': {}
            },
            'data': None
        }
        return Response(custom_response_data, status=status.HTTP_403_FORBIDDEN)
    
    # If response was handled by DRF default handler, customize the format
    if response is not None:
        custom_response_data = {
            'success': False,
            'message': 'Yêu cầu không hợp lệ',
            'error': {
                'code': 'VALIDATION_ERROR',
                'details': response.data
            },
            'data': None
        }
        response.data = custom_response_data
    
    return response
