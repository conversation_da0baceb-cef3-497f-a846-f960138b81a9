-- Hospital Management System - Test Data
-- Part 2: Insert Test Data
-- Database: QuanLyBenhVien

USE QuanLyBenhVien
GO

-- Insert Users
SET IDENTITY_INSERT NguoiDung ON;
INSERT INTO NguoiDung (<PERSON><PERSON><PERSON><PERSON><PERSON>, TenDang<PERSON>p, <PERSON>K<PERSON><PERSON>, <PERSON><PERSON>, Email, SoD<PERSON>, VaiTro) VALUES
(1, N'admin', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Nguyễn Văn Admin', N'<EMAIL>', N'0901234567', N'Admin'),
(2, N'bacsi1', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'<PERSON>r<PERSON><PERSON>', N'<EMAIL>', N'0902345678', N'<PERSON><PERSON><PERSON> s<PERSON>'),
(3, N'bacsi2', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Lê Văn Nam', N'<EMAIL>', N'0903456789', N'Bác sĩ'),
(4, N'bacsi3', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Phạm Minh Tuấn', N'<EMAIL>', N'0904567890', N'Bác sĩ'),
(5, N'bacsi4', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Nguyễn Thị Lan', N'<EMAIL>', N'0904567891', N'Bác sĩ'),
(6, N'yta1', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Nguyễn Thị Mai', N'<EMAIL>', N'0905678901', N'Y tá'),
(7, N'yta2', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Vũ Thị Lan', N'<EMAIL>', N'0906789012', N'Y tá'),
(8, N'yta3', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Trần Thị Thu', N'<EMAIL>', N'0906789013', N'Y tá'),
(9, N'letan1', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Hoàng Văn Đức', N'<EMAIL>', N'0907890123', N'Lễ tân'),
(10, N'letan2', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Phạm Thị Thảo', N'<EMAIL>', N'0907890124', N'Lễ tân'),
(11, N'ketoan1', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Đinh Thị Hoa', N'<EMAIL>', N'0908901234', N'Kế toán'),
(12, N'ketoan2', N'$2b$12$LQn3V6PyG8yH.W9Ss1kqCuWnqrEQz1xD8OsZQPtFbHQqKKqKqKqKq', N'Lý Văn Bình', N'<EMAIL>', N'0908901235', N'Kế toán');
SET IDENTITY_INSERT NguoiDung OFF;

-- Insert Departments
SET IDENTITY_INSERT KhoaPhong ON;
INSERT INTO KhoaPhong (MaKhoa, TenKhoa, LoaiKhoa, SoDienThoai, ViTri, MoTa) VALUES
(1, N'Khoa Nội tổng hợp', N'Nội khoa', N'0281234567', N'Tầng 2, Khu A', N'Khám và điều trị các bệnh nội khoa tổng quát'),
(2, N'Khoa Ngoại tổng hợp', N'Ngoại khoa', N'0281234568', N'Tầng 3, Khu A', N'Phẫu thuật và điều trị ngoại khoa'),
(3, N'Khoa Sản phụ khoa', N'Sản khoa', N'0281234569', N'Tầng 4, Khu B', N'Chăm sóc sức khỏe phụ nữ và thai sản'),
(4, N'Khoa Nhi', N'Nhi khoa', N'0281234570', N'Tầng 2, Khu B', N'Khám và điều trị bệnh cho trẻ em'),
(5, N'Khoa Cấp cứu', N'Cấp cứu', N'0281234571', N'Tầng 1, Khu A', N'Tiếp nhận và xử lý các ca cấp cứu 24/7'),
(6, N'Khoa Tim mạch', N'Nội khoa', N'0281234572', N'Tầng 5, Khu A', N'Chẩn đoán và điều trị bệnh tim mạch'),
(7, N'Khoa Thần kinh', N'Nội khoa', N'0281234573', N'Tầng 6, Khu A', N'Điều trị các bệnh về thần kinh');
SET IDENTITY_INSERT KhoaPhong OFF;
