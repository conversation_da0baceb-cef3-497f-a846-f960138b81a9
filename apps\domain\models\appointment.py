"""
Appointment Domain Model
Defines the Appointment entity for hospital appointment management.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from apps.domain.models.patient import <PERSON><PERSON><PERSON>han
from apps.domain.models.medical_staff import NhanVienYTe
from datetime import date, time, datetime


class LichHen(BaseEntity):
    """
    Appointment model for hospital management system.
    Represents scheduled appointments between patients and medical staff.
    """
    
    TRANG_THAI_CHOICES = [
        ('Scheduled', 'Đã lên lịch'),
        ('Confirmed', 'Đã xác nhận'),
        ('In_Progress', '<PERSON>ang khám'),
        ('Completed', 'Đã hoàn thành'),
        ('Cancelled', 'Đã hủy'),
        ('No_Show', 'Không đến'),
        ('Rescheduled', 'Đã chuyển lịch'),
    ]
    
    UU_TIEN_CHOICES = [
        ('Low', 'Thấp'),
        ('Normal', '<PERSON><PERSON><PERSON> thường'),
        ('High', '<PERSON>'),
        ('<PERSON>rgent', '<PERSON>h<PERSON>n cấp'),
    ]
    
    # Primary Key
    LichHenID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    benh_nhan = models.ForeignKey(
        BenhNhan,
        on_delete=models.CASCADE,
        related_name='lich_hen',
        verbose_name='Bệnh nhân',
        help_text='Bệnh nhân đặt lịch hẹn'
    )
    
    bac_si = models.ForeignKey(
        NhanVienYTe,
        on_delete=models.PROTECT,
        related_name='lich_hen_bac_si',
        verbose_name='Bác sĩ',
        help_text='Bác sĩ được hẹn khám',
        limit_choices_to={'loai_nhan_vien': 'Bac_Si'}
    )
    
    # Appointment Details
    ngay_hen = models.DateField(
        'Ngày hẹn',
        help_text='Ngày hẹn khám'
    )
    
    gio_hen = models.TimeField(
        'Giờ hẹn',
        help_text='Giờ hẹn khám'
    )
    
    gio_ket_thuc_du_kien = models.TimeField(
        'Giờ kết thúc dự kiến',
        blank=True,
        null=True,
        help_text='Giờ dự kiến kết thúc khám'
    )
    
    # Appointment Information
    ly_do_kham = models.TextField(
        'Lý do khám',
        help_text='Lý do đặt lịch khám bệnh'
    )
    
    trieu_chung = models.TextField(
        'Triệu chứng',
        blank=True,
        null=True,
        help_text='Triệu chứng của bệnh nhân'
    )
    
    uu_tien = models.CharField(
        'Mức độ ưu tiên',
        max_length=20,
        choices=UU_TIEN_CHOICES,
        default='Normal',
        help_text='Mức độ ưu tiên của cuộc hẹn'
    )
    
    # Status and Notes
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Scheduled',
        help_text='Trạng thái hiện tại của lịch hẹn'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú thêm về lịch hẹn'
    )
    
    # Contact Information
    so_dien_thoai_lien_he = models.CharField(
        'SĐT liên hệ',
        max_length=15,
        blank=True,
        null=True,
        help_text='Số điện thoại liên hệ cho lịch hẹn này'
    )
    
    # Timestamps
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo lịch hẹn'
    )
    
    ngay_xac_nhan = models.DateTimeField(
        'Ngày xác nhận',
        blank=True,
        null=True,
        help_text='Ngày xác nhận lịch hẹn'
    )
    
    gio_check_in = models.DateTimeField(
        'Giờ check-in',
        blank=True,
        null=True,
        help_text='Giờ bệnh nhân check-in'
    )
    
    gio_bat_dau_kham = models.DateTimeField(
        'Giờ bắt đầu khám',
        blank=True,
        null=True,
        help_text='Giờ thực tế bắt đầu khám'
    )
    
    gio_ket_thuc = models.DateTimeField(
        'Giờ kết thúc',
        blank=True,
        null=True,
        help_text='Giờ thực tế kết thúc khám'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật lần cuối'
    )
    
    class Meta:
        db_table = 'LichHen'
        verbose_name = 'Lịch hẹn'
        verbose_name_plural = 'Lịch hẹn'
        ordering = ['ngay_hen', 'gio_hen']
        indexes = [
            models.Index(fields=['ngay_hen', 'gio_hen']),
            models.Index(fields=['benh_nhan', 'trang_thai']),
            models.Index(fields=['bac_si', 'ngay_hen']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['uu_tien']),
            models.Index(fields=['ngay_tao']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['bac_si', 'ngay_hen', 'gio_hen'],
                condition=models.Q(trang_thai__in=['Scheduled', 'Confirmed', 'In_Progress']),
                name='unique_doctor_appointment_time'
            ),
        ]
    
    def __str__(self):
        return f"{self.benh_nhan.ho_ten} - BS.{self.bac_si.ho_ten} ({self.ngay_hen} {self.gio_hen})"
    
    def save(self, *args, **kwargs):
        """Override save to validate appointment data."""
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate appointment fields."""
        # Validate appointment date is not in the past
        if self.ngay_hen and self.ngay_hen < date.today():
            raise ValidationError({
                'ngay_hen': 'Ngày hẹn không thể trong quá khứ'
            })
        
        # Validate appointment time is in working hours (7 AM - 6 PM)
        if self.gio_hen:
            if self.gio_hen < time(7, 0) or self.gio_hen > time(18, 0):
                raise ValidationError({
                    'gio_hen': 'Giờ hẹn phải trong khung giờ làm việc (7:00 - 18:00)'
                })
        
        # Validate end time is after start time
        if self.gio_hen and self.gio_ket_thuc_du_kien:
            if self.gio_ket_thuc_du_kien <= self.gio_hen:
                raise ValidationError({
                    'gio_ket_thuc_du_kien': 'Giờ kết thúc phải sau giờ bắt đầu'
                })
        
        # Validate doctor is active and can treat patients
        if self.bac_si and not self.bac_si.can_treat_patients():
            raise ValidationError({
                'bac_si': 'Bác sĩ được chọn không thể khám bệnh'
            })
        
        # Validate patient is active
        if self.benh_nhan and not self.benh_nhan.is_active_patient():
            raise ValidationError({
                'benh_nhan': 'Bệnh nhân không trong trạng thái có thể khám'
            })
    
    # Business Methods
    def is_scheduled(self) -> bool:
        """Check if appointment is scheduled."""
        return self.trang_thai == 'Scheduled'
    
    def is_confirmed(self) -> bool:
        """Check if appointment is confirmed."""
        return self.trang_thai == 'Confirmed'
    
    def is_completed(self) -> bool:
        """Check if appointment is completed."""
        return self.trang_thai == 'Completed'
    
    def is_cancelled(self) -> bool:
        """Check if appointment is cancelled."""
        return self.trang_thai == 'Cancelled'
    
    def is_in_progress(self) -> bool:
        """Check if appointment is currently in progress."""
        return self.trang_thai == 'In_Progress'
    
    def is_urgent(self) -> bool:
        """Check if appointment is urgent."""
        return self.uu_tien == 'Urgent'
    
    def is_today(self) -> bool:
        """Check if appointment is today."""
        return self.ngay_hen == date.today()
    
    def is_overdue(self) -> bool:
        """Check if appointment is overdue."""
        if not self.is_scheduled() and not self.is_confirmed():
            return False
        
        now = timezone.now()
        appointment_datetime = datetime.combine(self.ngay_hen, self.gio_hen)
        return appointment_datetime < now.replace(tzinfo=None)
    
    def get_appointment_datetime(self) -> datetime:
        """Get appointment as datetime object."""
        return datetime.combine(self.ngay_hen, self.gio_hen)
    
    def get_duration_minutes(self) -> int:
        """Get appointment duration in minutes."""
        if not self.gio_ket_thuc_du_kien:
            return 30  # Default 30 minutes
        
        start_datetime = datetime.combine(date.today(), self.gio_hen)
        end_datetime = datetime.combine(date.today(), self.gio_ket_thuc_du_kien)
        
        duration = end_datetime - start_datetime
        return int(duration.total_seconds() / 60)
    
    def confirm_appointment(self, confirmed_by: str = None):
        """Confirm the appointment."""
        if self.trang_thai != 'Scheduled':
            raise ValueError("Chỉ có thể xác nhận lịch hẹn đã được lên lịch")
        
        self.trang_thai = 'Confirmed'
        self.ngay_xac_nhan = timezone.now()
        if confirmed_by:
            self.ghi_chu = f"Được xác nhận bởi: {confirmed_by}"
        
        self.save(update_fields=['trang_thai', 'ngay_xac_nhan', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_appointment_confirmed_event()
    
    def cancel_appointment(self, reason: str, cancelled_by: str = None):
        """Cancel the appointment."""
        if self.trang_thai in ['Completed', 'Cancelled']:
            raise ValueError("Không thể hủy lịch hẹn đã hoàn thành hoặc đã hủy")
        
        old_status = self.trang_thai
        self.trang_thai = 'Cancelled'
        
        cancel_note = f"Hủy lịch: {reason}"
        if cancelled_by:
            cancel_note += f" (bởi {cancelled_by})"
        
        self.ghi_chu = cancel_note
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_appointment_cancelled_event(reason, old_status)
    
    def reschedule_appointment(self, new_date: date, new_time: time, reason: str = None):
        """Reschedule the appointment."""
        if self.trang_thai in ['Completed', 'Cancelled', 'In_Progress']:
            raise ValueError("Không thể chuyển lịch hẹn đã hoàn thành, hủy hoặc đang tiến hành")
        
        old_date = self.ngay_hen
        old_time = self.gio_hen
        
        self.ngay_hen = new_date
        self.gio_hen = new_time
        self.trang_thai = 'Rescheduled'
        
        reschedule_note = f"Chuyển từ {old_date} {old_time} sang {new_date} {new_time}"
        if reason:
            reschedule_note += f" - Lý do: {reason}"
        
        self.ghi_chu = reschedule_note
        self.save(update_fields=['ngay_hen', 'gio_hen', 'trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_appointment_rescheduled_event(old_date, old_time, new_date, new_time, reason)
    
    def check_in_patient(self):
        """Check in patient for appointment."""
        if not self.is_confirmed() and not self.is_scheduled():
            raise ValueError("Chỉ có thể check-in cho lịch hẹn đã xác nhận")
        
        self.gio_check_in = timezone.now()
        self.save(update_fields=['gio_check_in', 'ngay_cap_nhat'])
        
        # Update patient's last visit
        self.benh_nhan.update_last_visit()
        
        # Raise domain event
        self.raise_patient_checked_in_event()
    
    def start_consultation(self):
        """Start the medical consultation."""
        if not self.gio_check_in:
            raise ValueError("Bệnh nhân chưa check-in")
        
        self.trang_thai = 'In_Progress'
        self.gio_bat_dau_kham = timezone.now()
        self.save(update_fields=['trang_thai', 'gio_bat_dau_kham', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_consultation_started_event()
    
    def complete_consultation(self, diagnosis: str = None, treatment_notes: str = None):
        """Complete the medical consultation."""
        if self.trang_thai != 'In_Progress':
            raise ValueError("Lịch hẹn không trong trạng thái khám")
        
        self.trang_thai = 'Completed'
        self.gio_ket_thuc = timezone.now()
        
        completion_notes = []
        if diagnosis:
            completion_notes.append(f"Chẩn đoán: {diagnosis}")
        if treatment_notes:
            completion_notes.append(f"Ghi chú điều trị: {treatment_notes}")
        
        if completion_notes:
            self.ghi_chu = "; ".join(completion_notes)
        
        self.save(update_fields=['trang_thai', 'gio_ket_thuc', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_consultation_completed_event(diagnosis, treatment_notes)
    
    def mark_no_show(self):
        """Mark appointment as no-show."""
        if not self.is_overdue():
            raise ValueError("Chỉ có thể đánh dấu no-show cho lịch hẹn quá hạn")
        
        self.trang_thai = 'No_Show'
        self.ghi_chu = 'Bệnh nhân không đến khám'
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_appointment_no_show_event()
    
    @property
    def display_status(self) -> str:
        """Get display status with priority."""
        status = self.get_trang_thai_display()
        if self.is_urgent():
            status += " (KHẨN CẤP)"
        return status
    
    @property
    def appointment_summary(self) -> dict:
        """Get appointment summary."""
        return {
            'patient': self.benh_nhan.ho_ten,
            'doctor': self.bac_si.ho_ten,
            'date': self.ngay_hen.strftime('%d/%m/%Y'),
            'time': self.gio_hen.strftime('%H:%M'),
            'status': self.get_trang_thai_display(),
            'priority': self.get_uu_tien_display(),
            'reason': self.ly_do_kham,
            'duration': self.get_duration_minutes()
        }
    
    # Domain Events
    def raise_appointment_created_event(self):
        """Raise appointment created domain event."""
        from apps.domain.events.domain_events import AppointmentCreatedEvent
        event = AppointmentCreatedEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            appointment_date=self.ngay_hen,
            appointment_time=self.gio_hen,
            reason=self.ly_do_kham,
            priority=self.uu_tien,
            created_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_appointment_confirmed_event(self):
        """Raise appointment confirmed domain event."""
        from apps.domain.events.domain_events import AppointmentConfirmedEvent
        event = AppointmentConfirmedEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            appointment_date=self.ngay_hen,
            appointment_time=self.gio_hen,
            confirmed_at=self.ngay_xac_nhan
        )
        self.add_domain_event(event)
    
    def raise_appointment_cancelled_event(self, reason: str, old_status: str):
        """Raise appointment cancelled domain event."""
        from apps.domain.events.domain_events import AppointmentCancelledEvent
        event = AppointmentCancelledEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            appointment_date=self.ngay_hen,
            appointment_time=self.gio_hen,
            cancellation_reason=reason,
            old_status=old_status,
            cancelled_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_appointment_rescheduled_event(self, old_date: date, old_time: time,
                                          new_date: date, new_time: time, reason: str = None):
        """Raise appointment rescheduled domain event."""
        from apps.domain.events.domain_events import AppointmentRescheduledEvent
        event = AppointmentRescheduledEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            old_date=old_date,
            old_time=old_time,
            new_date=new_date,
            new_time=new_time,
            reschedule_reason=reason,
            rescheduled_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_patient_checked_in_event(self):
        """Raise patient checked in domain event."""
        from apps.domain.events.domain_events import PatientCheckedInEvent
        event = PatientCheckedInEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            checked_in_at=self.gio_check_in
        )
        self.add_domain_event(event)
    
    def raise_consultation_started_event(self):
        """Raise consultation started domain event."""
        from apps.domain.events.domain_events import ConsultationStartedEvent
        event = ConsultationStartedEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            started_at=self.gio_bat_dau_kham
        )
        self.add_domain_event(event)
    
    def raise_consultation_completed_event(self, diagnosis: str = None, treatment_notes: str = None):
        """Raise consultation completed domain event."""
        from apps.domain.events.domain_events import ConsultationCompletedEvent
        event = ConsultationCompletedEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            diagnosis=diagnosis,
            treatment_notes=treatment_notes,
            completed_at=self.gio_ket_thuc
        )
        self.add_domain_event(event)
    
    def raise_appointment_no_show_event(self):
        """Raise appointment no show domain event."""
        from apps.domain.events.domain_events import AppointmentNoShowEvent
        event = AppointmentNoShowEvent(
            appointment_id=self.LichHenID,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            appointment_date=self.ngay_hen,
            appointment_time=self.gio_hen,
            no_show_at=timezone.now()
        )
        self.add_domain_event(event)
