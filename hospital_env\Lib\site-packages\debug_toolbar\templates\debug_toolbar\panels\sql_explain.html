{% load i18n %}
<div class="djDebugPanelTitle">
  <button type="button" class="djDebugClose">»</button>
  <h3>{% trans "SQL explained" %}</h3>
</div>
<div class="djDebugPanelContent">
  <div class="djdt-scroll">
    <dl>
      <dt>{% trans "Executed SQL" %}</dt>
      <dd>{{ sql|safe }}</dd>
      <dt>{% trans "Time" %}</dt>
      <dd>{{ duration }} ms</dd>
      <dt>{% trans "Database" %}</dt>
      <dd>{{ alias }}</dd>
    </dl>
    <table>
      <thead>
        <tr>
          {% for h in headers %}
            <th>{{ h|upper }}</th>
          {% endfor %}
        </tr>
      </thead>
      <tbody>
        {% for row in result %}
          <tr>
            {% for column in row %}
              <td>{% if forloop.last %}<code>{% endif %}{{ column|escape }}{% if forloop.last %}</code>{% endif %}</td>
            {% endfor %}
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
