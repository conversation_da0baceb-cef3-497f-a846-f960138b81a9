-- Hospital Management System - Complete Test Data
-- Part 4: Rooms, Medications, Appointments, Prescriptions, Bills
-- Database: QuanLyBenhVien

USE QuanLyBenhVien
GO

-- Insert Rooms
SET IDENTITY_INSERT PhongBenh ON;
INSERT INTO PhongBenh (MaP<PERSON>, TenPhong, MaKhoa, Loai<PERSON>, SoGiuong, SoGiuongTrong, GiaTheoNgay, TrangThai, TienNghi) VALUES
(1, N'P.101', 1, N'Thường', 4, 2, 500000, N'Sẵn sàng', <PERSON>'<PERSON><PERSON><PERSON>u hòa, TV, Tủ lạnh'),
(2, N'P.102', 1, N'VIP', 1, 1, 2000000, <PERSON>'Sẵn sàng', <PERSON>'<PERSON><PERSON><PERSON> h<PERSON>, TV, <PERSON><PERSON>, <PERSON>fa, Phòng tắm riêng'),
(3, N'P.201', 2, <PERSON>'Thường', 4, 3, 500000, <PERSON>'Sẵn sàng', <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hòa, TV'),
(4, N'P.202', 2, N'Thường', 4, 0, 500000, <PERSON><PERSON><PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>, TV'),
(5, <PERSON>'<PERSON><PERSON>301', 3, <PERSON>'VIP', 1, 0, 2500000, <PERSON>'<PERSON><PERSON>y', <PERSON>'<PERSON> tiện nghi cao cấp'),
(6, N'<PERSON>.302', 3, N'Th<PERSON>ờng', 2, 1, 700000, N'Sẵn sàng', N'<PERSON>iều hòa, <PERSON>, Tủ l<PERSON>nh'),
(7, N'<PERSON>.401', 4, N'<PERSON>h<PERSON>ờng', 6, 4, 450000, N'Sẵn sàng', N'Điều hòa, TV, Khu vui chơi'),
(8, N'ICU-01', 5, N'ICU', 1, 0, 5000000, N'Đầy', N'Thiết bị y tế chuyên dụng'),
(9, N'ICU-02', 5, N'ICU', 1, 1, 5000000, N'Sẵn sàng', N'Thiết bị y tế chuyên dụng'),
(10, N'ER-01', 5, N'Cấp cứu', 2, 2, 1000000, N'Sẵn sàng', N'Thiết bị cấp cứu'),
(11, N'P.103', 1, N'Thường', 4, 4, 500000, N'Sẵn sàng', N'Điều hòa, TV'),
(12, N'P.104', 1, N'VIP', 1, 0, 2000000, N'Đầy', N'VIP amenities'),
(13, N'P.203', 2, N'Thường', 4, 2, 500000, N'Sẵn sàng', N'Điều hòa, TV'),
(14, N'P.303', 3, N'Thường', 2, 2, 700000, N'Sẵn sàng', N'Điều hòa, TV'),
(15, N'P.402', 4, N'Thường', 6, 6, 450000, N'Sẵn sàng', N'Điều hòa, TV, Khu vui chơi');
SET IDENTITY_INSERT PhongBenh OFF;

-- Insert Medications
SET IDENTITY_INSERT Thuoc ON;
INSERT INTO Thuoc (MaThuoc, TenThuoc, LoaiThuoc, DonViTinh, SoLuongTon, GiaNhap, GiaBan, NgaySanXuat, NgayHetHan, NhaSanXuat, MoTa, CanhBao) VALUES
(1, N'Paracetamol 500mg', N'Giảm đau, hạ sốt', N'Viên', 1000, 500, 1000, '2024-01-01', '2026-01-01', N'Dược phẩm Hậu Giang', N'Thuốc giảm đau hạ sốt', 100),
(2, N'Amoxicillin 500mg', N'Kháng sinh', N'Viên', 500, 2000, 3500, '2024-02-01', '2026-02-01', N'Dược phẩm Imexpharm', N'Kháng sinh nhóm penicillin', 50),
(3, N'Vitamin C 1000mg', N'Vitamin', N'Viên', 2000, 1500, 2500, '2024-01-15', '2026-01-15', N'DHG Pharma', N'Bổ sung vitamin C', 200),
(4, N'Omeprazole 20mg', N'Dạ dày', N'Viên', 800, 3000, 5000, '2024-03-01', '2026-03-01', N'Traphaco', N'Thuốc điều trị dạ dày', 80),
(5, N'Metformin 850mg', N'Tiểu đường', N'Viên', 600, 1800, 3000, '2024-02-15', '2026-02-15', N'Stella', N'Thuốc tiểu đường', 60),
(6, N'Amlodipine 5mg', N'Tim mạch', N'Viên', 400, 4000, 6500, '2024-01-20', '2026-01-20', N'Dược phẩm OPC', N'Thuốc huyết áp', 40),
(7, N'Cetirizine 10mg', N'Dị ứng', N'Viên', 1500, 1200, 2000, '2024-03-10', '2026-03-10', N'Dược phẩm Hậu Giang', N'Thuốc chống dị ứng', 150),
(8, N'Ibuprofen 400mg', N'Giảm đau', N'Viên', 1200, 1500, 2500, '2024-02-20', '2026-02-20', N'Imexpharm', N'Thuốc giảm đau', 120),
(9, N'Losartan 50mg', N'Huyết áp', N'Viên', 350, 5000, 8000, '2024-01-25', '2026-01-25', N'DHG Pharma', N'Thuốc huyết áp', 35),
(10, N'Simvastatin 20mg', N'Cholesterol', N'Viên', 450, 4500, 7000, '2024-03-05', '2026-03-05', N'Traphaco', N'Thuốc cholesterol', 45),
(11, N'Aspirin 100mg', N'Tim mạch', N'Viên', 900, 800, 1500, '2024-01-10', '2026-01-10', N'Stella', N'Thuốc tim mạch', 90),
(12, N'Insulin glargine', N'Tiểu đường', N'Lọ', 100, 150000, 250000, '2024-02-01', '2025-08-01', N'Sanofi', N'Insulin dài hạn', 10),
(13, N'Clopidogrel 75mg', N'Tim mạch', N'Viên', 300, 8000, 12000, '2024-01-15', '2026-01-15', N'Teva', N'Thuốc chống đông máu', 30),
(14, N'Atorvastatin 20mg', N'Cholesterol', N'Viên', 250, 6000, 9500, '2024-02-10', '2026-02-10', N'Pfizer', N'Thuốc cholesterol', 25),
(15, N'Diazepam 5mg', N'Thần kinh', N'Viên', 200, 3000, 5000, '2024-01-05', '2026-01-05', N'Roche', N'Thuốc an thần', 20);
SET IDENTITY_INSERT Thuoc OFF;

-- Insert Appointments
SET IDENTITY_INSERT LichHen ON;
INSERT INTO LichHen (MaLichHen, MaBenhNhan, MaBacSi, NgayHen, GioHen, LyDoKham, TrieuChung, TrangThai, GhiChu) VALUES
(1, 1, 1, '2025-02-01', '09:00', N'Khám định kỳ', N'Đau bụng, khó tiêu', N'Đã đặt', N'Bệnh nhân cần nhịn ăn sáng'),
(2, 2, 2, '2025-02-01', '10:00', N'Khám bệnh', N'Đau ngực, khó thở', N'Đã đặt', N'Cần làm xét nghiệm máu'),
(3, 3, 3, '2025-02-02', '08:30', N'Khám thai', N'Khám thai định kỳ', N'Đã đặt', N'Lần khám thứ 3'),
(4, 4, 1, '2025-02-02', '14:00', N'Tái khám', N'Đau dạ dày', N'Đã đặt', N'Tái khám sau 2 tuần'),
(5, 5, 2, '2025-02-03', '09:30', N'Khám sức khỏe', N'Khám sức khỏe tổng quát', N'Đã đặt', NULL),
(6, 1, 1, '2025-01-15', '10:00', N'Khám bệnh', N'Sốt, ho', N'Đã khám', N'Đã kê đơn thuốc'),
(7, 2, 3, '2025-01-20', '11:00', N'Khám phụ khoa', N'Kiểm tra định kỳ', N'Đã khám', NULL),
(8, 6, 1, '2025-02-04', '15:00', N'Khám dị ứng', N'Nổi mẩn ngứa', N'Đã đặt', NULL),
(9, 7, 2, '2025-01-25', '16:00', N'Khám tim mạch', N'Đau ngực, tim đập nhanh', N'Đã khám', N'Cần theo dõi'),
(10, 8, 4, '2025-02-05', '08:00', N'Khám nhi', N'Sốt cao, quấy khóc', N'Đã đặt', N'Trẻ 3 tuổi'),
(11, 9, 1, '2025-01-30', '13:00', N'Khám thận', N'Đau lưng, tiểu khó', N'Đã khám', N'Cần siêu âm'),
(12, 10, 3, '2025-02-06', '09:00', N'Khám phụ khoa', N'Khám định kỳ', N'Đã đặt', NULL),
(13, 11, 2, '2025-01-28', '14:30', N'Khám gan', N'Vàng da, mệt mỏi', N'Đã khám', N'Cần xét nghiệm'),
(14, 12, 1, '2025-02-07', '10:30', N'Khám xương khớp', N'Đau khớp, cứng khớp', N'Đã đặt', NULL),
(15, 13, 4, '2025-02-08', '11:00', N'Khám nhi', N'Ho, sổ mũi', N'Đã đặt', N'Trẻ 5 tuổi'),
(16, 14, 1, '2025-01-22', '15:30', N'Khám viêm khớp', N'Đau khớp gối', N'Đã khám', N'Tiêm khớp'),
(17, 15, 2, '2025-02-09', '08:30', N'Khám lưng', N'Đau lưng mãn tính', N'Đã đặt', NULL),
(18, 16, 3, '2025-02-10', '14:00', N'Khám phụ khoa', N'Rối loạn kinh nguyệt', N'Đã đặt', NULL),
(19, 17, 1, '2025-01-26', '16:30', N'Khám huyết áp', N'Huyết áp cao', N'Đã khám', N'Kê đơn thuốc'),
(20, 18, 1, '2025-02-11', '09:15', N'Khám tiểu đường', N'Đường huyết cao', N'Đã đặt', N'Bệnh nhân type 1');
SET IDENTITY_INSERT LichHen OFF;

-- Insert Prescriptions
SET IDENTITY_INSERT DonThuoc ON;
INSERT INTO DonThuoc (MaDonThuoc, MaLichHen, MaBacSi, MaBenhNhan, NgayKeDon, ChanDoan, LoiDan, NgayTaiKham, TongTien, TrangThai) VALUES
(1, 6, 1, 1, '2025-01-15 10:30:00', N'Viêm đường hô hấp trên', N'Uống thuốc theo đúng liều lượng, nghỉ ngơi đầy đủ', '2025-01-29', 15000, N'Đã cấp thuốc'),
(2, 7, 3, 2, '2025-01-20 11:30:00', N'Viêm phụ khoa nhẹ', N'Vệ sinh cá nhân sạch sẽ, uống đủ nước', '2025-02-03', 25000, N'Đã cấp thuốc'),
(3, 9, 2, 7, '2025-01-25 16:30:00', N'Rối loạn nhịp tim', N'Hạn chế caffeine, tập thể dục nhẹ', '2025-02-08', 45000, N'Đã cấp thuốc'),
(4, 11, 1, 9, '2025-01-30 13:30:00', N'Viêm đường tiết niệu', N'Uống nhiều nước, vệ sinh sạch sẽ', '2025-02-13', 20000, N'Đã cấp thuốc'),
(5, 13, 2, 11, '2025-01-28 15:00:00', N'Viêm gan nhẹ', N'Kiêng rượu bia, ăn nhẹ', '2025-02-11', 35000, N'Đã cấp thuốc'),
(6, 16, 1, 14, '2025-01-22 16:00:00', N'Viêm khớp gối', N'Nghỉ ngơi, không vận động mạnh', '2025-02-05', 40000, N'Đã cấp thuốc'),
(7, 19, 1, 17, '2025-01-26 17:00:00', N'Tăng huyết áp', N'Ăn ít muối, tập thể dục đều đặn', '2025-02-09', 30000, N'Đã cấp thuốc');
SET IDENTITY_INSERT DonThuoc OFF;

-- Insert Prescription Details
SET IDENTITY_INSERT ChiTietDonThuoc ON;
INSERT INTO ChiTietDonThuoc (MaChiTiet, MaDonThuoc, MaThuoc, SoLuong, LieuDung, CachDung, SoNgay, DonGia, ThanhTien) VALUES
(1, 1, 1, 20, N'500mg x 2 lần/ngày', N'Sau ăn', 10, 1000, 20000),
(2, 1, 2, 14, N'500mg x 2 lần/ngày', N'Trước ăn 1 tiếng', 7, 3500, 49000),
(3, 2, 4, 14, N'20mg x 1 lần/ngày', N'Trước ăn sáng', 14, 5000, 70000),
(4, 2, 7, 10, N'10mg x 1 lần/ngày', N'Tối trước ngủ', 10, 2000, 20000),
(5, 3, 6, 30, N'5mg x 1 lần/ngày', N'Sáng', 30, 6500, 195000),
(6, 3, 11, 30, N'100mg x 1 lần/ngày', N'Sau ăn sáng', 30, 1500, 45000),
(7, 4, 2, 21, N'500mg x 3 lần/ngày', N'Sau ăn', 7, 3500, 73500),
(8, 4, 1, 15, N'500mg khi đau', N'Sau ăn', 15, 1000, 15000),
(9, 5, 4, 30, N'20mg x 2 lần/ngày', N'Trước ăn', 15, 5000, 150000),
(10, 5, 3, 30, N'1000mg x 1 lần/ngày', N'Sau ăn', 30, 2500, 75000),
(11, 6, 8, 20, N'400mg x 2 lần/ngày', N'Sau ăn', 10, 2500, 50000),
(12, 6, 7, 10, N'10mg x 1 lần/ngày', N'Tối', 10, 2000, 20000),
(13, 7, 9, 30, N'50mg x 1 lần/ngày', N'Sáng', 30, 8000, 240000),
(14, 7, 6, 30, N'5mg x 1 lần/ngày', N'Tối', 30, 6500, 195000);
SET IDENTITY_INSERT ChiTietDonThuoc OFF;

-- Update prescription totals
UPDATE DonThuoc SET TongTien = 69000 WHERE MaDonThuoc = 1;
UPDATE DonThuoc SET TongTien = 90000 WHERE MaDonThuoc = 2;
UPDATE DonThuoc SET TongTien = 240000 WHERE MaDonThuoc = 3;
UPDATE DonThuoc SET TongTien = 88500 WHERE MaDonThuoc = 4;
UPDATE DonThuoc SET TongTien = 225000 WHERE MaDonThuoc = 5;
UPDATE DonThuoc SET TongTien = 70000 WHERE MaDonThuoc = 6;
UPDATE DonThuoc SET TongTien = 435000 WHERE MaDonThuoc = 7;

-- Insert Bills
SET IDENTITY_INSERT HoaDon ON;
INSERT INTO HoaDon (MaHoaDon, MaBenhNhan, NgayLap, TienKham, TienThuoc, TienPhong, TienDichVu, TongTien, BaoHiemChiTra, BenhNhanTra, PhuongThucThanhToan, TrangThai, GhiChu, NguoiLap) VALUES
(1, 1, '2025-01-15 11:00:00', 200000, 69000, 0, 0, 269000, 200000, 69000, N'Tiền mặt', N'Đã thanh toán', N'Khám + thuốc', 9),
(2, 2, '2025-01-20 12:00:00', 300000, 90000, 0, 50000, 440000, 330000, 110000, N'Chuyển khoản', N'Đã thanh toán', N'Khám chuyên khoa', 9),
(3, 7, '2025-01-25 17:00:00', 400000, 240000, 0, 100000, 740000, 555000, 185000, N'Thẻ tín dụng', N'Đã thanh toán', N'Khám tim mạch', 11),
(4, 9, '2025-01-30 14:00:00', 200000, 88500, 0, 30000, 318500, 239000, 79500, N'Tiền mặt', N'Đã thanh toán', N'Khám + xét nghiệm', 9),
(5, 11, '2025-01-28 15:30:00', 250000, 225000, 0, 80000, 555000, 416000, 139000, N'Chuyển khoản', N'Đã thanh toán', N'Khám + xét nghiệm gan', 11),
(6, 14, '2025-01-22 16:30:00', 200000, 70000, 0, 150000, 420000, 315000, 105000, N'Tiền mặt', N'Đã thanh toán', N'Khám + tiêm khớp', 9),
(7, 17, '2025-01-26 17:30:00', 200000, 435000, 0, 0, 635000, 476000, 159000, N'Chuyển khoản', N'Đã thanh toán', N'Khám + thuốc huyết áp', 11),
(8, 3, '2025-02-02 09:00:00', 300000, 0, 700000, 0, 1000000, 0, 1000000, N'', N'Chưa thanh toán', N'Khám thai + phòng', 9),
(9, 5, '2025-02-03 10:00:00', 500000, 0, 0, 200000, 700000, 0, 700000, N'', N'Chưa thanh toán', N'Khám tổng quát', 9),
(10, 8, '2025-02-05 08:30:00', 250000, 0, 450000, 0, 700000, 0, 700000, N'', N'Chưa thanh toán', N'Khám nhi + phòng', 9);
SET IDENTITY_INSERT HoaDon OFF;

PRINT 'Database setup completed successfully!';
PRINT 'Total Users: 12';
PRINT 'Total Departments: 7';  
PRINT 'Total Medical Staff: 7';
PRINT 'Total Patients: 52';
PRINT 'Total Rooms: 15';
PRINT 'Total Medications: 15';
PRINT 'Total Appointments: 20';
PRINT 'Total Prescriptions: 7';
PRINT 'Total Bills: 10';
