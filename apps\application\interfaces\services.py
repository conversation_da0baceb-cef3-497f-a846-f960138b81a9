"""
Application Service Interfaces
Defines contracts for application layer services following Clean Architecture.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime, date


class IUserService(ABC):
    """User management service interface."""
    
    @abstractmethod
    async def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user and return user data with tokens."""
        pass
    
    @abstractmethod
    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new user account."""
        pass
    
    @abstractmethod
    async def update_user_profile(self, user_id: int, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update user profile information."""
        pass
    
    @abstractmethod
    async def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """Change user password."""
        pass


class IPatientService(ABC):
    """Patient management service interface."""
    
    @abstractmethod
    async def register_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register new patient."""
        pass
    
    @abstractmethod
    async def update_patient_info(self, patient_id: int, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update patient information."""
        pass
    
    @abstractmethod
    async def get_patient_medical_history(self, patient_id: int) -> List[Dict[str, Any]]:
        """Get patient's medical history."""
        pass
    
    @abstractmethod
    async def search_patients(self, search_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search patients by various criteria."""
        pass


class IAppointmentService(ABC):
    """Appointment management service interface."""
    
    @abstractmethod
    async def schedule_appointment(self, appointment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Schedule new appointment."""
        pass
    
    @abstractmethod
    async def reschedule_appointment(self, appointment_id: int, new_datetime: datetime) -> Dict[str, Any]:
        """Reschedule existing appointment."""
        pass
    
    @abstractmethod
    async def cancel_appointment(self, appointment_id: int, reason: str) -> bool:
        """Cancel appointment."""
        pass
    
    @abstractmethod
    async def get_doctor_schedule(self, doctor_id: int, date: date) -> List[Dict[str, Any]]:
        """Get doctor's schedule for specific date."""
        pass
    
    @abstractmethod
    async def check_appointment_conflicts(self, doctor_id: int, datetime: datetime) -> bool:
        """Check for appointment conflicts."""
        pass


class IMedicalStaffService(ABC):
    """Medical staff management service interface."""
    
    @abstractmethod
    async def create_staff_member(self, staff_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new medical staff member."""
        pass
    
    @abstractmethod
    async def update_staff_schedule(self, staff_id: int, schedule_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update staff working schedule."""
        pass
    
    @abstractmethod
    async def get_available_doctors(self, department_id: int, datetime: datetime) -> List[Dict[str, Any]]:
        """Get available doctors in department at specific time."""
        pass


class IMedicationService(ABC):
    """Medication management service interface."""
    
    @abstractmethod
    async def create_medication(self, medication_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new medication to inventory."""
        pass
    
    @abstractmethod
    async def update_stock(self, medication_id: int, quantity: int, operation: str) -> Dict[str, Any]:
        """Update medication stock (add/subtract)."""
        pass
    
    @abstractmethod
    async def check_expiring_medications(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """Get medications expiring within specified days."""
        pass
    
    @abstractmethod
    async def get_low_stock_medications(self) -> List[Dict[str, Any]]:
        """Get medications with low stock levels."""
        pass


class IPrescriptionService(ABC):
    """Prescription management service interface."""
    
    @abstractmethod
    async def create_prescription(self, prescription_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new prescription."""
        pass
    
    @abstractmethod
    async def validate_prescription(self, prescription_data: Dict[str, Any]) -> List[str]:
        """Validate prescription data and return errors."""
        pass
    
    @abstractmethod
    async def dispense_prescription(self, prescription_id: int) -> Dict[str, Any]:
        """Mark prescription as dispensed and update inventory."""
        pass


class IBillingService(ABC):
    """Billing and payment service interface."""
    
    @abstractmethod
    async def create_bill(self, billing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new bill for patient."""
        pass
    
    @abstractmethod
    async def process_payment(self, bill_id: int, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process payment for bill."""
        pass
    
    @abstractmethod
    async def apply_insurance(self, bill_id: int, insurance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply insurance coverage to bill."""
        pass
    
    @abstractmethod
    async def generate_invoice(self, bill_id: int) -> bytes:
        """Generate PDF invoice for bill."""
        pass


class IRoomService(ABC):
    """Room management service interface."""
    
    @abstractmethod
    async def check_room_availability(self, room_type: str, date_range: Dict[str, date]) -> List[Dict[str, Any]]:
        """Check available rooms for date range."""
        pass
    
    @abstractmethod
    async def assign_room(self, patient_id: int, room_id: int, start_date: date) -> Dict[str, Any]:
        """Assign room to patient."""
        pass
    
    @abstractmethod
    async def release_room(self, room_id: int, patient_id: int) -> bool:
        """Release room from patient."""
        pass


class IReportService(ABC):
    """Report generation service interface."""
    
    @abstractmethod
    async def generate_patient_report(self, patient_id: int, report_type: str) -> bytes:
        """Generate patient-specific report."""
        pass
    
    @abstractmethod
    async def generate_financial_report(self, date_range: Dict[str, date], format: str) -> bytes:
        """Generate financial report for date range."""
        pass
    
    @abstractmethod
    async def generate_inventory_report(self, format: str) -> bytes:
        """Generate inventory status report."""
        pass
    
    @abstractmethod
    async def generate_staff_performance_report(self, staff_id: int, date_range: Dict[str, date]) -> bytes:
        """Generate staff performance report."""
        pass
