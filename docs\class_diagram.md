# Hospital Management System - Class Diagram

## Overview
This class diagram represents the core domain entities and their relationships following SOLID principles and Clean Architecture patterns.

## Class Diagram

```mermaid
classDiagram
    %% Domain Models
    class NguoiDung {
        +int MaNguoiDung
        +string TenDangNhap
        +string MatKhau
        +string HoTen
        +string Email
        +string SoDienThoai
        +VaiTro VaiTro
        +bool TrangThai
        +DateTime NgayTao
        +DateTime NgayCapNhat
        +validate()
        +hashPassword()
        +checkPassword()
    }

    class KhoaPhong {
        +int MaKhoa
        +string TenKhoa
        +string LoaiKhoa
        +int MaTruongKhoa
        +string SoDienThoai
        +string ViTri
        +string MoTa
        +DateTime NgayTao
        +getTotalStaff()
        +getAvailableRooms()
    }

    class NhanVienYTe {
        +int MaNhanVien
        +int MaNguoiDung
        +int MaKhoa
        +LoaiNhanVien LoaiNhanVien
        +string ChuyenKhoa
        +string BangCap
        +int KinhNghiem
        +string GioLamViec
        +decimal LuongCoBan
        +Date NgayVaoLam
        +string TrangThai
        +isAvailable(DateTime time)
        +getWorkingHours()
        +calculateSalary()
    }

    class BenhNhan {
        +int MaBenhNhan
        +string HoTen
        +Date NgaySinh
        +GioiTinh GioiTinh
        +string CMND
        +string DiaChi
        +string SoDienThoai
        +string Email
        +string NhomMau
        +string TienSuBenh
        +string DiUng
        +string BaoHiemYTe
        +DateTime NgayDangKy
        +string TrangThai
        +calculateAge()
        +getMedicalHistory()
        +hasInsurance()
    }

    class PhongBenh {
        +int MaPhong
        +string TenPhong
        +int MaKhoa
        +LoaiPhong LoaiPhong
        +int SoGiuong
        +int SoGiuongTrong
        +decimal GiaTheoNgay
        +string TrangThai
        +string TienNghi
        +isAvailable()
        +calculateCost(int days)
        +reserveBed()
        +releaseBed()
    }

    class LichHen {
        +int MaLichHen
        +int MaBenhNhan
        +int MaBacSi
        +Date NgayHen
        +Time GioHen
        +string LyDoKham
        +string TrieuChung
        +TrangThaiLichHen TrangThai
        +string GhiChu
        +DateTime NgayTao
        +isConflict(LichHen other)
        +canCancel()
        +reschedule(DateTime newTime)
    }

    class Thuoc {
        +int MaThuoc
        +string TenThuoc
        +string LoaiThuoc
        +string DonViTinh
        +int SoLuongTon
        +decimal GiaNhap
        +decimal GiaBan
        +Date NgaySanXuat
        +Date NgayHetHan
        +string NhaSanXuat
        +string MoTa
        +int CanhBao
        +string TrangThai
        +isExpired()
        +needsRestock()
        +updateStock(int quantity)
    }

    class DonThuoc {
        +int MaDonThuoc
        +int MaLichHen
        +int MaBacSi
        +int MaBenhNhan
        +DateTime NgayKeDon
        +string ChanDoan
        +string LoiDan
        +Date NgayTaiKham
        +decimal TongTien
        +string TrangThai
        +calculateTotal()
        +addMedication()
        +validatePrescription()
    }

    class ChiTietDonThuoc {
        +int MaChiTiet
        +int MaDonThuoc
        +int MaThuoc
        +int SoLuong
        +string LieuDung
        +string CachDung
        +int SoNgay
        +decimal DonGia
        +decimal ThanhTien
        +calculateSubtotal()
        +validateDosage()
    }

    class HoaDon {
        +int MaHoaDon
        +int MaBenhNhan
        +DateTime NgayLap
        +decimal TienKham
        +decimal TienThuoc
        +decimal TienPhong
        +decimal TienDichVu
        +decimal TongTien
        +decimal BaoHiemChiTra
        +decimal BenhNhanTra
        +string PhuongThucThanhToan
        +TrangThaiHoaDon TrangThai
        +string GhiChu
        +int NguoiLap
        +calculateTotal()
        +applyInsurance()
        +processPayment()
    }

    %% Enums
    class VaiTro {
        <<enumeration>>
        Admin
        BacSi
        YTa
        LeTan
        KeToan
    }

    class GioiTinh {
        <<enumeration>>
        Nam
        Nu
        Khac
    }

    class LoaiNhanVien {
        <<enumeration>>
        BacSi
        YTa
    }

    class LoaiPhong {
        <<enumeration>>
        VIP
        Thuong
        ICU
        CapCuu
    }

    class TrangThaiLichHen {
        <<enumeration>>
        DaDat
        DaKham
        Huy
        KhongDen
    }

    class TrangThaiHoaDon {
        <<enumeration>>
        ChuaThanhToan
        DaThanhToan
        Huy
    }

    %% Relationships
    NguoiDung ||--|| NhanVienYTe : "1:1"
    KhoaPhong ||--o{ NhanVienYTe : "1:N"
    KhoaPhong ||--o{ PhongBenh : "1:N"
    NhanVienYTe ||--o{ KhoaPhong : "heads"
    
    BenhNhan ||--o{ LichHen : "1:N"
    NhanVienYTe ||--o{ LichHen : "1:N"
    
    LichHen ||--o| DonThuoc : "1:0..1"
    NhanVienYTe ||--o{ DonThuoc : "prescribes"
    BenhNhan ||--o{ DonThuoc : "1:N"
    
    DonThuoc ||--o{ ChiTietDonThuoc : "1:N"
    Thuoc ||--o{ ChiTietDonThuoc : "1:N"
    
    BenhNhan ||--o{ HoaDon : "1:N"
    NguoiDung ||--o{ HoaDon : "creates"

    %% Aggregations and Compositions
    NguoiDung --* NhanVienYTe : composition
    DonThuoc *-- ChiTietDonThuoc : composition
    KhoaPhong o-- PhongBenh : aggregation
```

## Design Patterns Applied

### 1. **Single Responsibility Principle (SRP)**
- Each class has a single responsibility
- `BenhNhan` handles patient data only
- `LichHen` manages appointments only
- `HoaDon` handles billing only

### 2. **Open/Closed Principle (OCP)**
- Classes are open for extension, closed for modification
- `NhanVienYTe` can be extended for different staff types
- `PhongBenh` can support new room types through enums

### 3. **Liskov Substitution Principle (LSP)**
- Derived classes can substitute base classes
- Different `VaiTro` implementations maintain consistent behavior

### 4. **Interface Segregation Principle (ISP)**
- Interfaces are specific to client needs
- Methods are focused and relevant to each class

### 5. **Dependency Inversion Principle (DIP)**
- High-level modules don't depend on low-level modules
- Dependencies are inverted through abstractions

## Key Relationships

### **Composition Relationships**
- `NguoiDung` *composes* `NhanVienYTe` (cannot exist without user)
- `DonThuoc` *composes* `ChiTietDonThuoc` (prescription details depend on prescription)

### **Aggregation Relationships**
- `KhoaPhong` *aggregates* `PhongBenh` (rooms can exist without department)
- `KhoaPhong` *aggregates* `NhanVienYTe` (staff can move between departments)

### **Association Relationships**
- `BenhNhan` *associates with* `LichHen` (many appointments)
- `NhanVienYTe` *associates with* `LichHen` (doctor-patient appointments)
- `Thuoc` *associates with* `ChiTietDonThuoc` (medication usage)

## Business Logic Methods

### **Validation Methods**
- `NguoiDung.validate()`: Validates user data
- `DonThuoc.validatePrescription()`: Checks prescription validity
- `ChiTietDonThuoc.validateDosage()`: Validates medication dosage

### **Calculation Methods**
- `BenhNhan.calculateAge()`: Calculates patient age
- `PhongBenh.calculateCost()`: Calculates room costs
- `HoaDon.calculateTotal()`: Calculates bill totals
- `HoaDon.applyInsurance()`: Applies insurance discounts

### **Business Rule Methods**
- `LichHen.isConflict()`: Checks appointment conflicts
- `Thuoc.isExpired()`: Checks medication expiry
- `NhanVienYTe.isAvailable()`: Checks staff availability
- `PhongBenh.isAvailable()`: Checks room availability

## Data Integrity Constraints

1. **Referential Integrity**: Foreign key relationships maintain data consistency
2. **Domain Constraints**: Enums ensure valid values for status fields
3. **Business Rules**: Methods enforce hospital business logic
4. **Validation**: Input validation prevents invalid data entry

This design promotes maintainability, scalability, and follows enterprise-level architecture patterns.
