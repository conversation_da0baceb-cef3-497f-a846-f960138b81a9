# Hospital Management System API

🏥 **A comprehensive REST API for Hospital Management System built with Django and Django REST Framework**

Final assignment of Python subject in UIT. This project implements a complete hospital management system with Clean Architecture principles, providing REST API endpoints for managing patients, medical staff, appointments, prescriptions, billing, and more.

## 📋 Table of Contents

- [Features](#features)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Installation & Setup](#installation--setup)
- [Database Configuration](#database-configuration)
- [Running the Application](#running-the-application)
- [API Documentation](#api-documentation)
- [Authentication](#authentication)
- [API Endpoints](#api-endpoints)
- [Usage Examples](#usage-examples)
- [Admin Interface](#admin-interface)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)

## ✨ Features

### 👥 User Management
- Role-based authentication (Admin, Doctor, Pharmacist, Nurse, Receptionist, Accountant)
- User profiles with detailed information
- Permission-based access control

### 🏥 Core Hospital Operations
- **Patient Management**: Registration, medical history, insurance tracking
- **Medical Staff Management**: Doctor/nurse profiles, specializations, schedules
- **Department Management**: Hospital departments with staff assignments
- **Room Management**: Room allocation, capacity tracking, pricing

### 📅 Appointment System
- Appointment scheduling and management
- Doctor availability tracking
- Appointment status workflow (Scheduled → Confirmed → Completed → Cancelled)
- Automated conflict detection

### 💊 Prescription Management
- Electronic prescription creation by doctors
- Pharmacist dispensing workflow
- Medication inventory integration
- Prescription expiry tracking

### 💰 Billing System
- Comprehensive billing for examinations, medications, and services
- Multiple payment methods support
- Insurance coverage processing
- Payment tracking and overdue management

### 📦 Inventory Management
- Medication stock tracking
- Low stock alerts
- Expiry date monitoring
- Automatic stock calculations

### 📊 Reporting & Analytics
- Dashboard with key statistics
- Revenue tracking
- Patient and appointment analytics
- Real-time system monitoring

## 🛠 Technology Stack

- **Backend Framework**: Django 4.2
- **API Framework**: Django REST Framework
- **Database**: Microsoft SQL Server
- **Authentication**: Token-based + Session authentication
- **Documentation**: Auto-generated API docs
- **Architecture**: Clean Architecture with Domain-Driven Design
- **Language**: Python 3.9+

## 📁 Project Structure

```
HospitalManagementAPI/
├── apps/
│   ├── application/          # Application layer (Use cases, CQRS)
│   ├── domain/              # Domain layer (Models, Business logic)
│   ├── infrastructure/      # Infrastructure layer (Database, Cache, etc.)
│   └── presentation/        # Presentation layer (API endpoints)
├── hospital_management/     # Django project settings
├── database/               # SQL scripts for database setup
├── docs/                  # Project documentation
├── static/               # Static files
├── media/               # Media uploads
├── logs/               # Application logs
├── requirements.txt    # Python dependencies
├── manage.py          # Django management script
└── .env.example      # Environment variables template

```

## 🚀 Installation & Setup

### Prerequisites

- Python 3.9 or higher
- Microsoft SQL Server (2019 or later)
- SQL Server ODBC Driver 17
- Git

### Step 1: Clone the Repository

```bash
git clone https://github.com/your-username/HospitalManagementAPI.git
cd HospitalManagementAPI
```

### Step 2: Create Virtual Environment

```bash
# Create virtual environment
python -m venv hospital_env

# Activate virtual environment
# On Windows:
hospital_env\Scripts\activate
# On macOS/Linux:
source hospital_env/bin/activate
```

### Step 3: Install Dependencies

```bash
pip install -r requirements.txt
```

### Step 4: Environment Configuration

1. Copy the environment template:
```bash
copy .env.example .env
```

2. Edit `.env` file with your configuration:
```env
# Database Configuration
DB_NAME=HospitalManagement
DB_USER=sa
DB_PASSWORD=YourStrong!Passw0rd
DB_HOST=localhost
DB_PORT=1433

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True

# Hospital Information
HOSPITAL_NAME=Bệnh viện Đa khoa ABC
HOSPITAL_ADDRESS=123 Đường XYZ, Quận ABC, TP.HCM
HOSPITAL_PHONE=(028) 1234-5678
HOSPITAL_EMAIL=<EMAIL>
```

## 🗄️ Database Configuration

### Step 1: Install SQL Server

Download and install Microsoft SQL Server from the official website.

### Step 2: Create Database

1. Open SQL Server Management Studio (SSMS)
2. Connect to your SQL Server instance
3. Run the database creation script:

```bash
# Navigate to database folder
cd database

# Execute SQL scripts in order:
# 1. Create database and tables
sqlcmd -S localhost -U sa -P YourPassword -i 01_create_database.sql

# 2. Insert test data
sqlcmd -S localhost -U sa -P YourPassword -i 02_insert_test_data.sql

# 3. Insert additional data (optional)
sqlcmd -S localhost -U sa -P YourPassword -i 03_insert_more_data.sql
sqlcmd -S localhost -U sa -P YourPassword -i 04_complete_test_data.sql
```

### Step 3: Django Database Setup

```bash
# Make migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser account
python manage.py createsuperuser
```

## 🏃‍♂️ Running the Application

### Development Server

```bash
# Start the development server
python manage.py runserver

# The API will be available at:
# http://localhost:8000/api/
# Admin panel: http://localhost:8000/admin/
```

### Production Server

```bash
# Collect static files
python manage.py collectstatic

# Run with Gunicorn (install separately)
pip install gunicorn
gunicorn hospital_management.wsgi:application --bind 0.0.0.0:8000
```

## 📚 API Documentation

Once the server is running, you can access the API documentation at:

- **Browsable API**: `http://localhost:8000/api/`
- **Admin Interface**: `http://localhost:8000/admin/`

## 🔐 Authentication

The API uses Token-based authentication. Here's how to authenticate:

### Step 1: Login to Get Token

```bash
POST /api/auth/login/
Content-Type: application/json

{
    "username": "your_username",
    "password": "your_password"
}
```

Response:
```json
{
    "token": "your-auth-token-here",
    "user": {
        "NguoiDungID": 1,
        "username": "admin",
        "ho_ten": "Administrator",
        "vai_tro": "Quan_Tri_Vien"
    },
    "message": "Đăng nhập thành công"
}
```

### Step 2: Use Token in Requests

Include the token in the Authorization header:

```bash
Authorization: Token your-auth-token-here
```

## 🛣️ API Endpoints

### Authentication
- `POST /api/auth/login/` - User login
- `POST /api/users/change_password/` - Change password
- `GET /api/users/profile/` - Get user profile

### Patient Management
- `GET /api/patients/` - List all patients
- `POST /api/patients/` - Create new patient
- `GET /api/patients/{id}/` - Get patient details
- `PUT /api/patients/{id}/` - Update patient
- `DELETE /api/patients/{id}/` - Delete patient
- `GET /api/patients/{id}/appointments/` - Get patient appointments
- `GET /api/patients/{id}/prescriptions/` - Get patient prescriptions
- `GET /api/patients/{id}/bills/` - Get patient bills

### Medical Staff Management
- `GET /api/medical-staff/` - List all medical staff
- `POST /api/medical-staff/` - Create new staff member
- `GET /api/medical-staff/{id}/` - Get staff details
- `GET /api/medical-staff/{id}/appointments/` - Get staff appointments
- `GET /api/medical-staff/{id}/schedule/` - Get staff schedule

### Appointment Management
- `GET /api/appointments/` - List all appointments
- `POST /api/appointments/` - Create new appointment
- `GET /api/appointments/{id}/` - Get appointment details
- `POST /api/appointments/{id}/confirm/` - Confirm appointment
- `POST /api/appointments/{id}/cancel/` - Cancel appointment
- `POST /api/appointments/{id}/complete/` - Complete appointment

### Prescription Management
- `GET /api/prescriptions/` - List all prescriptions
- `POST /api/prescriptions/` - Create new prescription
- `GET /api/prescriptions/{id}/` - Get prescription details
- `POST /api/prescriptions/{id}/issue/` - Issue prescription
- `POST /api/prescriptions/{id}/dispense/` - Dispense prescription
- `POST /api/prescriptions/{id}/add_medication/` - Add medication to prescription

### Medication Management
- `GET /api/medications/` - List all medications
- `POST /api/medications/` - Create new medication
- `GET /api/medications/{id}/` - Get medication details
- `GET /api/medications/low_stock/` - Get low stock medications
- `GET /api/medications/expired/` - Get expired medications
- `POST /api/medications/{id}/add_stock/` - Add stock to medication

### Billing Management
- `GET /api/bills/` - List all bills
- `POST /api/bills/` - Create new bill
- `GET /api/bills/{id}/` - Get bill details
- `POST /api/bills/{id}/issue/` - Issue bill
- `POST /api/bills/{id}/process_payment/` - Process payment
- `POST /api/bills/{id}/apply_insurance/` - Apply insurance coverage

### Department & Room Management
- `GET /api/departments/` - List all departments
- `GET /api/departments/{id}/staff/` - Get department staff
- `GET /api/departments/{id}/rooms/` - Get department rooms
- `GET /api/rooms/` - List all rooms
- `GET /api/rooms/available/` - Get available rooms

### Dashboard & Statistics
- `GET /api/dashboard/stats/` - Get dashboard statistics
- `GET /api/dashboard/revenue/` - Get revenue statistics

## 💡 Usage Examples

### Create a New Patient

```bash
POST /api/patients/
Authorization: Token your-token-here
Content-Type: application/json

{
    "ho_ten": "Nguyễn Văn A",
    "ngay_sinh": "1990-01-15",
    "gioi_tinh": "Nam",
    "so_dien_thoai": "**********",
    "email": "<EMAIL>",
    "dia_chi": "123 Đường ABC, Quận 1, TP.HCM",
    "so_cccd": "123456789012",
    "so_bao_hiem": "HS1234567890",
    "loai_bao_hiem": "BHYT"
}
```

### Schedule an Appointment

```bash
POST /api/appointments/
Authorization: Token your-token-here
Content-Type: application/json

{
    "benh_nhan": 1,
    "bac_si": 2,
    "ngay_gio_hen": "2024-01-20T09:00:00",
    "loai_kham": "Kham_Tong_Quat",
    "ly_do_kham": "Khám sức khỏe định kỳ",
    "ghi_chu_benh_nhan": "Có tiền sử bệnh tim"
}
```

### Create a Prescription

```bash
POST /api/prescriptions/
Authorization: Token your-token-here
Content-Type: application/json

{
    "benh_nhan": 1,
    "bac_si": 2,
    "loai_don_thuoc": "Ngoai_Tru",
    "chan_doan": "Viêm họng cấp",
    "loi_dan_bac_si": "Uống thuốc sau ăn, nghỉ ngơi đầy đủ"
}
```

### Add Medication to Prescription

```bash
POST /api/prescriptions/1/add_medication/
Authorization: Token your-token-here
Content-Type: application/json

{
    "medication_id": 5,
    "quantity": 20,
    "dosage": "1 viên",
    "frequency": "2 lần/ngày",
    "duration": "10 ngày",
    "instructions": "Uống sau ăn 30 phút"
}
```

### Process Bill Payment

```bash
POST /api/bills/1/process_payment/
Authorization: Token your-token-here
Content-Type: application/json

{
    "amount": 500000,
    "payment_method": "Tien_Mat",
    "notes": "Thanh toán tiền mặt"
}
```

## 👨‍💼 Admin Interface

Access the Django admin interface at `http://localhost:8000/admin/` to:

- Manage users and permissions
- View and edit all data models
- Monitor system activity
- Generate reports
- Configure system settings

**Default Admin Credentials** (after running `createsuperuser`):
- Username: (as created)
- Password: (as created)

### Admin Features:
- **User Management**: Create/edit users with different roles
- **Data Management**: CRUD operations for all entities
- **System Monitoring**: View logs and system statistics
- **Vietnamese Interface**: Fully localized admin interface

## 🧪 Testing

### Run Unit Tests

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test apps.domain
python manage.py test apps.presentation

# Run with coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
coverage html  # Generate HTML report
```

### API Testing with Postman

1. Import the API collection (create one from the endpoints above)
2. Set up environment variables:
   - `base_url`: `http://localhost:8000`
   - `auth_token`: Your authentication token

### Sample Test Data

The database scripts include sample data for testing:
- 50+ patients with various conditions
- Multiple doctors and medical staff
- Sample appointments and prescriptions
- Test medications and inventory
- Sample bills and payments

## 🚀 Deployment

### Production Checklist

1. **Environment Configuration**:
   ```env
   DEBUG=False
   SECRET_KEY=your-production-secret-key
   ALLOWED_HOSTS=your-domain.com,www.your-domain.com
   ```

2. **Database Setup**:
   - Use production SQL Server instance
   - Configure proper backup procedures
   - Set up monitoring

3. **Security**:
   - Enable HTTPS
   - Configure CORS properly
   - Set up proper firewall rules
   - Use environment variables for sensitive data

4. **Performance**:
   - Configure caching (Redis recommended for production)
   - Set up media file serving (AWS S3 or similar)
   - Configure logging aggregation

5. **Monitoring**:
   - Set up health checks
   - Configure error tracking
   - Monitor database performance

### Docker Deployment (Optional)

```dockerfile
# Dockerfile example
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["gunicorn", "hospital_management.wsgi:application", "--bind", "0.0.0.0:8000"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

### Development Guidelines

- Follow PEP 8 style guidelines
- Write comprehensive tests
- Update documentation
- Use meaningful commit messages
- Follow Clean Architecture principles

## 📞 Support

For support and questions:

- **Email**: <EMAIL>
- **University**: University of Information Technology (UIT)
- **Subject**: Python Programming Final Assignment

## 📄 License

This project is developed as an academic assignment for the Python Programming course at UIT.

---

**Happy Coding! 🏥💻**
