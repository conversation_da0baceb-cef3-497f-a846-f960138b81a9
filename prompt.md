# HOSPITAL MANAGEMENT SYSTEM DEVELOPMENT PROMPT

## CONTEXT & ROLE
You are an **Expert Solution Architect** with 15+ years of experience in enterprise system design. You need to build a high-quality hospital management system that will be evaluated by top-tier Solution Architects and Engineering Managers on:
- Software architecture quality
- Clean code and best practices
- Design pattern implementation
- Scalability and maintainability

## SYSTEM REQUIREMENTS

### Technical Stack
- **Framework**: Django (Python)
- **Database**: Microsoft SQL Server (max 10 tables)
- **Scope**: Web API Backend only (no Frontend required)
- **Language**: Vietnamese with diacritics
- **Currency**: VND only
- **DB Config**: SA account, password "123"

### Architecture Requirements
- **OOP**: Complete Object-Oriented Programming
- **MVC Pattern**: Clear Model-View-Controller separation
- **SOLID Principles**: Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion
- **DRY Principle**: Don't Repeat Yourself
- **Design Patterns**: Repository, Factory, Strategy, Observer, etc.
- **Clean Architecture**: Separation of concerns, dependency inversion

## TASK BREAKDOWN

Execute in exact order, one task at a time:

### **TASK 1: SYSTEM ANALYSIS & DESIGN**
1. **Domain Analysis**: 
   - Analyze hospital domain (Patients, Doctors, Nurses, Departments, Rooms, Medications, Bills, Appointments, etc.)
   - Identify business rules and constraints
   
2. **Database Design**:
   - Design ERD with maximum 10 tables
   - Optimize relationships and indexing
   - Create SQL script with:
     - Database and table creation (database and table name in Vietnamese)
     - Test data insertion (minimum 50+ records) with various data scenario for testing all system function
     - Script can run multiple times without errors
     - Vietnamese data with diacritics, VND currency

3. **Class Diagram**:
   - Draw class diagram using Mermaid
   - Show relationships, inheritance, composition
   - Apply SOLID principles

### **TASK 2: ARCHITECTURE DESIGN**
1. **Project Structure**:
   - Organize folders following Clean Architecture
   - Clear separation of concerns
   - Layer separation (Domain, Application, Infrastructure, API)

2. **Design Patterns Planning**:
   - Repository Pattern for data access
   - Factory Pattern for object creation
   - Strategy Pattern for business logic
   - Observer Pattern for notifications
   - Dependency Injection setup

### **TASK 3: MODELS & DOMAIN LAYER**
1. **Django Models**:
   - Create models corresponding to database
   - Custom managers and querysets
   - Model validation and business rules
   
2. **Domain Services**:
   - Business logic encapsulation
   - Domain events handling
   - Value objects implementation

### **TASK 4: AUTHENTICATION & AUTHORIZATION**
1. **User Management**:
   - Custom User model
   - Role-based access control (RBAC)
   - JWT authentication
   - Permission system

2. **Security Implementation**:
   - Password hashing
   - Input validation
   - API rate limiting
   - Error handling

### **TASK 5: REPOSITORY & DATA ACCESS LAYER**
1. **Repository Pattern**:
   - Generic repository implementation
   - Specific repositories for each entity
   - Unit of Work pattern
   
2. **Database Integration**:
   - Django ORM optimization
   - Custom SQL when needed
   - Transaction management

### **TASK 6: APPLICATION SERVICES**
1. **Service Layer**:
   - Application services for use cases
   - Command/Query separation (CQRS concepts)
   - Input/Output DTOs
   
2. **Business Logic**:
   - Validation services
   - Calculation services
   - Notification services

### **TASK 7: API ENDPOINTS (VIEWS/CONTROLLERS)**
1. **REST API Design**:
   - RESTful endpoints for all entities
   - CRUD operations
   - Filtering, searching, pagination
   - Proper HTTP status codes

2. **Advanced Features**:
   - File upload/download
   - Report generation (PDF/Excel)
   - Bulk operations
   - API versioning

### **TASK 8: FEATURE IMPLEMENTATION**
Implement specific features for hospital management system:

1. **Patient Management**:
   - New patient registration
   - Patient information updates
   - Medical history tracking
   - Patient search functionality

2. **Doctor & Staff Management**:
   - Doctor information, specializations
   - Work schedules
   - Role-based permissions

3. **Appointment Management**:
   - Appointment scheduling
   - Conflict resolution
   - Reminder notifications
   - Calendar integration

4. **Medication & Inventory Management**:
   - Inventory management
   - Prescription handling
   - Stock alerts
   - Expiry date tracking

5. **Payment Management**:
   - Invoice generation
   - Payment processing
   - Insurance handling
   - Financial reports

6. **Reports & Analytics**:
   - Revenue reports
   - Patient statistics
   - Doctor performance metrics
   - Export to PDF/Excel

### **TASK 9: DOCUMENTATION & DEPLOYMENT**
1. **API Documentation**:
   - Swagger integration
   - Endpoint documentation
   - Authentication guide

2. **Deployment Setup**:
   - Settings configuration
   - Requirements.txt
   - Docker setup (optional)

## QUALITY CRITERIA

Each task must achieve:
- **Code Quality**: PEP 8, type hints, docstrings
- **Architecture**: Clear separation of concerns
- **Performance**: Optimized queries, caching where needed
- **Security**: Input validation, authentication, authorization
- **Maintainability**: Easy to extend and modify
- **Testability**: High test coverage

## DELIVERABLES PER TASK

For each task, provide:
1. **Code implementation** with full comments
2. **Explanation** of design decisions
3. **Architecture diagrams** if needed
4. **Testing approach** for that task
5. **Next steps** to connect with the following task

## EXECUTION INSTRUCTION

**START WITH TASK 1 FIRST. Complete 100% of current task before moving to the next one.**

After completing each task, ask: "Task [X] is completed. Would you like to review and provide feedback before moving to Task [X+1]?"

Ready to start with TASK 1: SYSTEM ANALYSIS & DESIGN?