"""
Payment Strategy Implementations
Implements Strategy pattern for different payment processing methods.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any
from decimal import Decimal
from datetime import datetime


class IPaymentStrategy(ABC):
    """Abstract payment strategy interface."""
    
    @abstractmethod
    async def process_payment(self, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process payment using specific strategy."""
        pass
    
    @abstractmethod
    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate payment data for this strategy."""
        pass
    
    @abstractmethod
    def get_payment_type(self) -> str:
        """Get payment type identifier."""
        pass


class CashPaymentStrategy(IPaymentStrategy):
    """Cash payment strategy implementation."""
    
    async def process_payment(self, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process cash payment."""
        received_amount = Decimal(str(payment_data.get('received_amount', 0)))
        
        if received_amount < amount:
            return {
                "success": False,
                "message": "<PERSON><PERSON> tiền nhận không đủ",
                "error_code": "INSUFFICIENT_CASH"
            }
        
        change = received_amount - amount
        
        return {
            "success": True,
            "transaction_id": f"CASH_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "payment_method": "Tiền mặt",
            "amount": float(amount),
            "received_amount": float(received_amount),
            "change": float(change),
            "processed_at": datetime.now().isoformat()
        }
    
    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate cash payment data."""
        return 'received_amount' in payment_data and float(payment_data['received_amount']) > 0
    
    def get_payment_type(self) -> str:
        return "CASH"


class CardPaymentStrategy(IPaymentStrategy):
    """Card payment strategy implementation."""
    
    async def process_payment(self, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process card payment."""
        # Simulate card processing
        card_number = payment_data.get('card_number', '')
        masked_card = f"****-****-****-{card_number[-4:]}" if len(card_number) >= 4 else "****"
        
        # In real implementation, integrate with payment gateway
        transaction_id = f"CARD_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        return {
            "success": True,
            "transaction_id": transaction_id,
            "payment_method": "Thẻ tín dụng/ghi nợ",
            "amount": float(amount),
            "card_number": masked_card,
            "card_type": payment_data.get('card_type', 'Unknown'),
            "processed_at": datetime.now().isoformat()
        }
    
    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate card payment data."""
        required_fields = ['card_number', 'expiry_month', 'expiry_year', 'cvv']
        return all(field in payment_data for field in required_fields)
    
    def get_payment_type(self) -> str:
        return "CARD"


class InsurancePaymentStrategy(IPaymentStrategy):
    """Insurance payment strategy implementation."""
    
    async def process_payment(self, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process insurance payment."""
        insurance_number = payment_data.get('insurance_number', '')
        coverage_percentage = float(payment_data.get('coverage_percentage', 80))
        
        # Calculate insurance coverage
        insurance_amount = amount * Decimal(coverage_percentage / 100)
        patient_amount = amount - insurance_amount
        
        return {
            "success": True,
            "transaction_id": f"INS_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "payment_method": "Bảo hiểm y tế",
            "total_amount": float(amount),
            "insurance_amount": float(insurance_amount),
            "patient_amount": float(patient_amount),
            "insurance_number": insurance_number,
            "coverage_percentage": coverage_percentage,
            "processed_at": datetime.now().isoformat()
        }
    
    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate insurance payment data."""
        return 'insurance_number' in payment_data and payment_data['insurance_number']
    
    def get_payment_type(self) -> str:
        return "INSURANCE"


class BankTransferStrategy(IPaymentStrategy):
    """Bank transfer payment strategy implementation."""
    
    async def process_payment(self, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process bank transfer payment."""
        bank_name = payment_data.get('bank_name', '')
        account_number = payment_data.get('account_number', '')
        reference_number = payment_data.get('reference_number', '')
        
        return {
            "success": True,
            "transaction_id": f"BANK_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "payment_method": "Chuyển khoản ngân hàng",
            "amount": float(amount),
            "bank_name": bank_name,
            "account_number": f"****{account_number[-4:]}" if len(account_number) >= 4 else "****",
            "reference_number": reference_number,
            "processed_at": datetime.now().isoformat()
        }
    
    async def validate_payment_data(self, payment_data: Dict[str, Any]) -> bool:
        """Validate bank transfer payment data."""
        required_fields = ['bank_name', 'account_number', 'reference_number']
        return all(field in payment_data for field in required_fields)
    
    def get_payment_type(self) -> str:
        return "BANK_TRANSFER"


class PaymentStrategyContext:
    """
    Payment strategy context for managing different payment methods.
    Implements Strategy pattern context.
    """
    
    def __init__(self):
        self._strategies: Dict[str, IPaymentStrategy] = {
            "CASH": CashPaymentStrategy(),
            "CARD": CardPaymentStrategy(),
            "INSURANCE": InsurancePaymentStrategy(),
            "BANK_TRANSFER": BankTransferStrategy()
        }
    
    def add_strategy(self, payment_type: str, strategy: IPaymentStrategy) -> None:
        """Add new payment strategy."""
        self._strategies[payment_type] = strategy
    
    def get_strategy(self, payment_type: str) -> IPaymentStrategy:
        """Get payment strategy by type."""
        if payment_type not in self._strategies:
            raise ValueError(f"Unsupported payment type: {payment_type}")
        return self._strategies[payment_type]
    
    async def process_payment(self, payment_type: str, amount: Decimal, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process payment using appropriate strategy."""
        strategy = self.get_strategy(payment_type)
        
        # Validate payment data first
        if not await strategy.validate_payment_data(payment_data):
            return {
                "success": False,
                "message": "Dữ liệu thanh toán không hợp lệ",
                "error_code": "INVALID_PAYMENT_DATA"
            }
        
        return await strategy.process_payment(amount, payment_data)
    
    def get_supported_payment_types(self) -> list:
        """Get list of supported payment types."""
        return list(self._strategies.keys())
