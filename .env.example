# Hospital Management System Environment Variables
# Copy this file to .env and update with your actual values

# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=QuanLyBenhVien
DB_USER=sa
DB_PASSWORD=123
DB_HOST=localhost
DB_PORT=1433

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379/1

# Celery Configuration
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# Email Configuration (Production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
