"""
Repository Pattern Implementation
Concrete implementations of repository interfaces for data access layer.
"""
from typing import List, Optional, Dict, Any
from django.db.models import QuerySet, Q
from django.core.exceptions import ObjectDoesNotExist
from datetime import datetime, date
from apps.domain.shared.interfaces import (
    IPatientRepository, IMedicalStaffRepository, IAppointmentRepository,
    IMedicationRepository, IPrescriptionRepository, IRoomRepository,
    IBillRepository, IUserRepository, IDepartmentRepository
)
from apps.infrastructure.exceptions.custom_exceptions import (
    PatientNotFoundException, MedicalStaffNotFoundException,
    AppointmentNotFoundException, MedicationNotFoundException,
    PrescriptionNotFoundException, RoomNotFoundException,
    BillNotFoundException, ResourceNotFoundException
)


class BaseRepository:
    """Base repository with common functionality."""
    
    def __init__(self, model_class):
        self.model_class = model_class
    
    async def get_by_id(self, entity_id: int):
        """Get entity by ID."""
        try:
            return await self.model_class.objects.aget(pk=entity_id)
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"Entity with ID {entity_id} not found")
    
    async def get_all(self) -> QuerySet:
        """Get all entities."""
        return self.model_class.objects.all()
    
    async def save(self, entity) -> Any:
        """Save entity."""
        await entity.asave()
        return entity
    
    async def delete(self, entity_id: int) -> bool:
        """Delete entity by ID."""
        try:
            entity = await self.get_by_id(entity_id)
            await entity.adelete()
            return True
        except ObjectDoesNotExist:
            return False
    
    async def count(self) -> int:
        """Count all entities."""
        return await self.model_class.objects.acount()
    
    async def exists(self, entity_id: int) -> bool:
        """Check if entity exists."""
        return await self.model_class.objects.filter(pk=entity_id).aexists()


class PatientRepository(BaseRepository, IPatientRepository):
    """Repository for Patient entities."""
    
    def __init__(self):
        # Will be injected with actual model when models are created
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, patient_id: int):
        """Get patient by ID."""
        try:
            return await self.model_class.objects.aget(pk=patient_id)
        except ObjectDoesNotExist:
            raise PatientNotFoundException(patient_id)
    
    async def get_by_cmnd(self, cmnd: str):
        """Get patient by CMND."""
        try:
            return await self.model_class.objects.aget(CMND=cmnd)
        except ObjectDoesNotExist:
            return None
    
    async def get_by_phone(self, phone: str):
        """Get patient by phone number."""
        try:
            return await self.model_class.objects.aget(SoDienThoai=phone)
        except ObjectDoesNotExist:
            return None
    
    async def search_patients(self, search_term: str = "", cmnd: str = "", 
                            phone: str = "", email: str = "", 
                            offset: int = 0, limit: int = 20) -> tuple:
        """Search patients with filters."""
        query = Q()
        
        if search_term:
            query |= (
                Q(HoTen__icontains=search_term) |
                Q(CMND__icontains=search_term) |
                Q(SoDienThoai__icontains=search_term) |
                Q(Email__icontains=search_term)
            )
        
        if cmnd:
            query &= Q(CMND__icontains=cmnd)
        
        if phone:
            query &= Q(SoDienThoai__icontains=phone)
        
        if email:
            query &= Q(Email__icontains=email)
        
        queryset = self.model_class.objects.filter(query).order_by('-NgayDangKy')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_medical_history(self, patient_id: int, date_from: date = None, 
                                date_to: date = None) -> List:
        """Get patient's medical history."""
        query = Q(BenhNhanID=patient_id)
        
        if date_from:
            query &= Q(NgayTao__gte=date_from)
        if date_to:
            query &= Q(NgayTao__lte=date_to)
        
        # This would need to be implemented when we have the actual models
        # Return appointments, prescriptions, and bills for the patient
        return []


class MedicalStaffRepository(BaseRepository, IMedicalStaffRepository):
    """Repository for Medical Staff entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, staff_id: int):
        """Get medical staff by ID."""
        try:
            return await self.model_class.objects.aget(pk=staff_id)
        except ObjectDoesNotExist:
            raise MedicalStaffNotFoundException(staff_id)
    
    async def get_available_doctors(self, department_id: int = None, 
                                  specialty: str = None, 
                                  date_time: datetime = None) -> List:
        """Get available doctors."""
        query = Q(LoaiNhanVien='Bac_Si')
        
        if department_id:
            query &= Q(KhoaPhongID=department_id)
        
        if specialty:
            query &= Q(ChuyenKhoa__icontains=specialty)
        
        doctors = self.model_class.objects.filter(query)
        
        if date_time:
            # Filter out doctors who have appointments at the specified time
            # This would need actual appointment model integration
            pass
        
        return await doctors.all()
    
    async def search_staff(self, search_term: str = "", staff_type: str = None,
                          department_id: int = None, offset: int = 0, 
                          limit: int = 20) -> tuple:
        """Search medical staff."""
        query = Q()
        
        if search_term:
            query |= (
                Q(HoTen__icontains=search_term) |
                Q(ChuyenKhoa__icontains=search_term) |
                Q(SoDienThoai__icontains=search_term)
            )
        
        if staff_type:
            query &= Q(LoaiNhanVien=staff_type)
        
        if department_id:
            query &= Q(KhoaPhongID=department_id)
        
        queryset = self.model_class.objects.filter(query).order_by('HoTen')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_doctor_schedule(self, doctor_id: int, date_from: date, 
                                date_to: date) -> List:
        """Get doctor's schedule."""
        # This would integrate with appointment model
        return []


class AppointmentRepository(BaseRepository, IAppointmentRepository):
    """Repository for Appointment entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, appointment_id: int):
        """Get appointment by ID."""
        try:
            return await self.model_class.objects.aget(pk=appointment_id)
        except ObjectDoesNotExist:
            raise AppointmentNotFoundException(appointment_id)
    
    async def get_by_patient(self, patient_id: int, status: str = None,
                           date_from: date = None, date_to: date = None,
                           offset: int = 0, limit: int = 20) -> tuple:
        """Get appointments by patient."""
        query = Q(BenhNhanID=patient_id)
        
        if status:
            query &= Q(TrangThai=status)
        if date_from:
            query &= Q(NgayHen__gte=date_from)
        if date_to:
            query &= Q(NgayHen__lte=date_to)
        
        queryset = self.model_class.objects.filter(query).order_by('-NgayHen', '-GioHen')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_by_doctor(self, doctor_id: int, appointment_date: date = None,
                          status: str = None) -> List:
        """Get appointments by doctor."""
        query = Q(BacSiID=doctor_id)
        
        if appointment_date:
            query &= Q(NgayHen=appointment_date)
        if status:
            query &= Q(TrangThai=status)
        
        return await self.model_class.objects.filter(query).order_by('GioHen').all()
    
    async def check_appointment_conflict(self, doctor_id: int, 
                                       appointment_date: date,
                                       appointment_time: str,
                                       exclude_appointment_id: int = None) -> bool:
        """Check for appointment conflicts."""
        query = Q(
            BacSiID=doctor_id,
            NgayHen=appointment_date,
            GioHen=appointment_time,
            TrangThai__in=['Confirmed', 'In_Progress']
        )
        
        if exclude_appointment_id:
            query &= ~Q(pk=exclude_appointment_id)
        
        return await self.model_class.objects.filter(query).aexists()


class MedicationRepository(BaseRepository, IMedicationRepository):
    """Repository for Medication entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, medication_id: int):
        """Get medication by ID."""
        try:
            return await self.model_class.objects.aget(pk=medication_id)
        except ObjectDoesNotExist:
            raise MedicationNotFoundException(medication_id)
    
    async def search_medications(self, search_term: str = "", 
                               medication_type: str = None,
                               low_stock_only: bool = False,
                               expiring_soon: bool = False,
                               days_until_expiry: int = 30,
                               offset: int = 0, limit: int = 20) -> tuple:
        """Search medications with filters."""
        query = Q()
        
        if search_term:
            query |= (
                Q(TenThuoc__icontains=search_term) |
                Q(HoatChatChinh__icontains=search_term) |
                Q(NhaSanXuat__icontains=search_term)
            )
        
        if medication_type:
            query &= Q(LoaiThuoc=medication_type)
        
        if low_stock_only:
            query &= Q(SoLuongTon__lte=10)  # Assuming 10 is low stock threshold
        
        if expiring_soon:
            from django.utils import timezone
            expiry_threshold = timezone.now().date() + timezone.timedelta(days=days_until_expiry)
            query &= Q(NgayHetHan__lte=expiry_threshold)
        
        queryset = self.model_class.objects.filter(query).order_by('TenThuoc')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_low_stock_medications(self, warning_level: int = 10) -> List:
        """Get medications with low stock."""
        return await self.model_class.objects.filter(
            SoLuongTon__lte=warning_level
        ).order_by('SoLuongTon').all()
    
    async def get_expiring_medications(self, days_ahead: int = 30) -> List:
        """Get medications expiring soon."""
        from django.utils import timezone
        expiry_threshold = timezone.now().date() + timezone.timedelta(days=days_ahead)
        
        return await self.model_class.objects.filter(
            NgayHetHan__lte=expiry_threshold
        ).order_by('NgayHetHan').all()
    
    async def update_stock(self, medication_id: int, quantity_change: int) -> bool:
        """Update medication stock."""
        try:
            medication = await self.get_by_id(medication_id)
            medication.SoLuongTon += quantity_change
            if medication.SoLuongTon < 0:
                medication.SoLuongTon = 0
            await medication.asave()
            return True
        except Exception:
            return False


class PrescriptionRepository(BaseRepository, IPrescriptionRepository):
    """Repository for Prescription entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, prescription_id: int):
        """Get prescription by ID."""
        try:
            return await self.model_class.objects.aget(pk=prescription_id)
        except ObjectDoesNotExist:
            raise PrescriptionNotFoundException(prescription_id)
    
    async def get_by_patient(self, patient_id: int, status: str = None,
                           date_from: date = None, date_to: date = None,
                           offset: int = 0, limit: int = 20) -> tuple:
        """Get prescriptions by patient."""
        query = Q(BenhNhanID=patient_id)
        
        if status:
            query &= Q(TrangThai=status)
        if date_from:
            query &= Q(NgayKeDon__gte=date_from)
        if date_to:
            query &= Q(NgayKeDon__lte=date_to)
        
        queryset = self.model_class.objects.filter(query).order_by('-NgayKeDon')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_by_doctor(self, doctor_id: int, date_from: date = None,
                          date_to: date = None, offset: int = 0, 
                          limit: int = 20) -> tuple:
        """Get prescriptions by doctor."""
        query = Q(BacSiID=doctor_id)
        
        if date_from:
            query &= Q(NgayKeDon__gte=date_from)
        if date_to:
            query &= Q(NgayKeDon__lte=date_to)
        
        queryset = self.model_class.objects.filter(query).order_by('-NgayKeDon')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count


class RoomRepository(BaseRepository, IRoomRepository):
    """Repository for Room entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, room_id: int):
        """Get room by ID."""
        try:
            return await self.model_class.objects.aget(pk=room_id)
        except ObjectDoesNotExist:
            raise RoomNotFoundException(room_id)
    
    async def get_available_rooms(self, room_type: str = None, 
                                department_id: int = None,
                                date_from: date = None, 
                                date_to: date = None) -> List:
        """Get available rooms."""
        query = Q(TrangThai='Available')
        
        if room_type:
            query &= Q(LoaiPhong=room_type)
        
        if department_id:
            query &= Q(KhoaPhongID=department_id)
        
        # Additional filtering based on booking dates would go here
        # when we have booking/reservation models
        
        return await self.model_class.objects.filter(query).order_by('SoPhong').all()
    
    async def get_room_occupancy(self, department_id: int = None, 
                               room_type: str = None) -> Dict[str, int]:
        """Get room occupancy statistics."""
        query = Q()
        
        if department_id:
            query &= Q(KhoaPhongID=department_id)
        
        if room_type:
            query &= Q(LoaiPhong=room_type)
        
        queryset = self.model_class.objects.filter(query)
        
        # This would need actual occupancy tracking
        return {
            'total_rooms': await queryset.acount(),
            'available_rooms': await queryset.filter(TrangThai='Available').acount(),
            'occupied_rooms': await queryset.filter(TrangThai='Occupied').acount(),
            'maintenance_rooms': await queryset.filter(TrangThai='Maintenance').acount()
        }


class BillRepository(BaseRepository, IBillRepository):
    """Repository for Bill entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_id(self, bill_id: int):
        """Get bill by ID."""
        try:
            return await self.model_class.objects.aget(pk=bill_id)
        except ObjectDoesNotExist:
            raise BillNotFoundException(bill_id)
    
    async def get_by_patient(self, patient_id: int, status: str = None,
                           date_from: date = None, date_to: date = None,
                           offset: int = 0, limit: int = 20) -> tuple:
        """Get bills by patient."""
        query = Q(BenhNhanID=patient_id)
        
        if status:
            query &= Q(TrangThai=status)
        if date_from:
            query &= Q(NgayTao__gte=date_from)
        if date_to:
            query &= Q(NgayTao__lte=date_to)
        
        queryset = self.model_class.objects.filter(query).order_by('-NgayTao')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_unpaid_bills(self, days_overdue: int = None,
                             offset: int = 0, limit: int = 20) -> tuple:
        """Get unpaid bills."""
        query = Q(TrangThai='Unpaid')
        
        if days_overdue:
            from django.utils import timezone
            overdue_date = timezone.now().date() - timezone.timedelta(days=days_overdue)
            query &= Q(NgayTao__lte=overdue_date)
        
        queryset = self.model_class.objects.filter(query).order_by('NgayTao')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count
    
    async def get_financial_summary(self, date_from: date, date_to: date) -> Dict[str, float]:
        """Get financial summary for period."""
        from django.db.models import Sum, Count
        
        query = Q(NgayTao__range=[date_from, date_to])
        
        result = await self.model_class.objects.filter(query).aggregate(
            total_revenue=Sum('TongTien'),
            paid_amount=Sum('TongTien', filter=Q(TrangThai='Paid')),
            unpaid_amount=Sum('TongTien', filter=Q(TrangThai='Unpaid')),
            total_bills=Count('id'),
            paid_bills=Count('id', filter=Q(TrangThai='Paid'))
        )
        
        return {
            'total_revenue': result['total_revenue'] or 0,
            'paid_amount': result['paid_amount'] or 0,
            'unpaid_amount': result['unpaid_amount'] or 0,
            'total_bills': result['total_bills'] or 0,
            'paid_bills': result['paid_bills'] or 0
        }


class UserRepository(BaseRepository, IUserRepository):
    """Repository for User entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_by_username(self, username: str):
        """Get user by username."""
        try:
            return await self.model_class.objects.aget(TenDangNhap=username)
        except ObjectDoesNotExist:
            return None
    
    async def search_users(self, search_term: str = "", role: str = None,
                          active_only: bool = True, offset: int = 0, 
                          limit: int = 20) -> tuple:
        """Search users."""
        query = Q()
        
        if search_term:
            query |= (
                Q(TenDangNhap__icontains=search_term) |
                Q(HoTen__icontains=search_term) |
                Q(Email__icontains=search_term)
            )
        
        if role:
            query &= Q(VaiTro=role)
        
        if active_only:
            query &= Q(TrangThai='Active')
        
        queryset = self.model_class.objects.filter(query).order_by('TenDangNhap')
        total_count = await queryset.acount()
        results = queryset[offset:offset + limit]
        
        return results, total_count


class DepartmentRepository(BaseRepository, IDepartmentRepository):
    """Repository for Department entities."""
    
    def __init__(self):
        super().__init__(None)  # Placeholder for now
    
    async def get_all_with_staff_count(self) -> List:
        """Get all departments with staff count."""
        # This would need to be implemented with proper joins
        return await self.model_class.objects.all().prefetch_related('nhanvienyte_set')
    
    async def get_by_id_with_details(self, department_id: int, 
                                   include_staff: bool = False,
                                   include_rooms: bool = False):
        """Get department with detailed information."""
        try:
            query = self.model_class.objects
            
            if include_staff:
                query = query.prefetch_related('nhanvienyte_set')
            
            if include_rooms:
                query = query.prefetch_related('phongbenh_set')
            
            return await query.aget(pk=department_id)
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"Department with ID {department_id} not found")
