"""
Domain Events Implementation
Implements Observer pattern for domain event handling and business logic notifications.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Type
from datetime import datetime
from apps.domain.shared.base_entity import DomainEvent


class AppointmentScheduledEvent(DomainEvent):
    """Event fired when a new appointment is scheduled."""
    
    def __init__(self, appointment_id: int, patient_id: int, doctor_id: int, appointment_time: datetime):
        super().__init__()
        self.appointment_id = appointment_id
        self.patient_id = patient_id
        self.doctor_id = doctor_id
        self.appointment_time = appointment_time


class AppointmentCancelledEvent(DomainEvent):
    """Event fired when an appointment is cancelled."""
    
    def __init__(self, appointment_id: int, patient_id: int, doctor_id: int, reason: str):
        super().__init__()
        self.appointment_id = appointment_id
        self.patient_id = patient_id
        self.doctor_id = doctor_id
        self.reason = reason


class AppointmentCompletedEvent(DomainEvent):
    """Event fired when an appointment is completed."""
    
    def __init__(self, appointment_id: int, patient_id: int, doctor_id: int, diagnosis: str = None):
        super().__init__()
        self.appointment_id = appointment_id
        self.patient_id = patient_id
        self.doctor_id = doctor_id
        self.diagnosis = diagnosis


class PrescriptionCreatedEvent(DomainEvent):
    """Event fired when a prescription is created."""
    
    def __init__(self, prescription_id: int, patient_id: int, doctor_id: int, medications: List[Dict]):
        super().__init__()
        self.prescription_id = prescription_id
        self.patient_id = patient_id
        self.doctor_id = doctor_id
        self.medications = medications


class PrescriptionDispensedEvent(DomainEvent):
    """Event fired when a prescription is dispensed."""
    
    def __init__(self, prescription_id: int, patient_id: int, dispensed_by: int):
        super().__init__()
        self.prescription_id = prescription_id
        self.patient_id = patient_id
        self.dispensed_by = dispensed_by


class MedicationLowStockEvent(DomainEvent):
    """Event fired when medication stock is low."""
    
    def __init__(self, medication_id: int, medication_name: str, current_stock: int, warning_level: int):
        super().__init__()
        self.medication_id = medication_id
        self.medication_name = medication_name
        self.current_stock = current_stock
        self.warning_level = warning_level


class MedicationExpiredEvent(DomainEvent):
    """Event fired when medication expires."""
    
    def __init__(self, medication_id: int, medication_name: str, expiry_date: datetime, quantity: int):
        super().__init__()
        self.medication_id = medication_id
        self.medication_name = medication_name
        self.expiry_date = expiry_date
        self.quantity = quantity


class PatientRegisteredEvent(DomainEvent):
    """Event fired when a new patient is registered."""
    
    def __init__(self, patient_id: int, patient_name: str, contact_info: Dict[str, str]):
        super().__init__()
        self.patient_id = patient_id
        self.patient_name = patient_name
        self.contact_info = contact_info


class PaymentProcessedEvent(DomainEvent):
    """Event fired when a payment is processed."""
    
    def __init__(self, bill_id: int, patient_id: int, amount: float, payment_method: str, transaction_id: str):
        super().__init__()
        self.bill_id = bill_id
        self.patient_id = patient_id
        self.amount = amount
        self.payment_method = payment_method
        self.transaction_id = transaction_id


class BillCreatedEvent(DomainEvent):
    """Event fired when a bill is created."""
    
    def __init__(self, bill_id: int, patient_id: int, total_amount: float, services: List[Dict]):
        super().__init__()
        self.bill_id = bill_id
        self.patient_id = patient_id
        self.total_amount = total_amount
        self.services = services


class RoomAssignedEvent(DomainEvent):
    """Event fired when a room is assigned to a patient."""
    
    def __init__(self, room_id: int, patient_id: int, assignment_date: datetime, room_type: str):
        super().__init__()
        self.room_id = room_id
        self.patient_id = patient_id
        self.assignment_date = assignment_date
        self.room_type = room_type


class RoomReleasedEvent(DomainEvent):
    """Event fired when a room is released."""
    
    def __init__(self, room_id: int, patient_id: int, release_date: datetime, duration_days: int):
        super().__init__()
        self.room_id = room_id
        self.patient_id = patient_id
        self.release_date = release_date
        self.duration_days = duration_days


class IDomainEventHandler(ABC):
    """Abstract domain event handler interface."""
    
    @abstractmethod
    async def handle(self, event: DomainEvent) -> None:
        """Handle domain event."""
        pass


class DomainEventDispatcher:
    """
    Domain event dispatcher implementing Observer pattern.
    Manages event handlers and dispatches events to appropriate handlers.
    """
    
    def __init__(self):
        self._handlers: Dict[Type[DomainEvent], List[IDomainEventHandler]] = {}
    
    def subscribe(self, event_type: Type[DomainEvent], handler: IDomainEventHandler) -> None:
        """Subscribe handler to specific event type."""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
    
    def unsubscribe(self, event_type: Type[DomainEvent], handler: IDomainEventHandler) -> None:
        """Unsubscribe handler from event type."""
        if event_type in self._handlers:
            try:
                self._handlers[event_type].remove(handler)
            except ValueError:
                pass  # Handler not found
    
    async def dispatch(self, event: DomainEvent) -> None:
        """Dispatch event to all registered handlers."""
        event_type = type(event)
        
        if event_type in self._handlers:
            for handler in self._handlers[event_type]:
                try:
                    await handler.handle(event)
                except Exception as e:
                    # Log error but don't stop other handlers
                    print(f"Error handling event {event_type.__name__}: {str(e)}")
    
    async def dispatch_all(self, events: List[DomainEvent]) -> None:
        """Dispatch multiple events."""
        for event in events:
            await self.dispatch(event)
    
    def get_handlers(self, event_type: Type[DomainEvent]) -> List[IDomainEventHandler]:
        """Get all handlers for specific event type."""
        return self._handlers.get(event_type, []).copy()
    
    def clear_handlers(self, event_type: Type[DomainEvent] = None) -> None:
        """Clear handlers for specific event type or all handlers."""
        if event_type:
            self._handlers.pop(event_type, None)
        else:
            self._handlers.clear()


# Global event dispatcher instance
domain_event_dispatcher = DomainEventDispatcher()


class EventHandlerRegistry:
    """Registry for automatic event handler discovery and registration."""
    
    def __init__(self, dispatcher: DomainEventDispatcher):
        self.dispatcher = dispatcher
        self._registered_handlers: List[IDomainEventHandler] = []
    
    def register_handler(self, handler: IDomainEventHandler, event_types: List[Type[DomainEvent]]) -> None:
        """Register handler for multiple event types."""
        for event_type in event_types:
            self.dispatcher.subscribe(event_type, handler)
        self._registered_handlers.append(handler)
    
    def unregister_handler(self, handler: IDomainEventHandler, event_types: List[Type[DomainEvent]]) -> None:
        """Unregister handler from multiple event types."""
        for event_type in event_types:
            self.dispatcher.unsubscribe(event_type, handler)
        try:
            self._registered_handlers.remove(handler)
        except ValueError:
            pass
    
    def get_registered_handlers(self) -> List[IDomainEventHandler]:
        """Get all registered handlers."""
        return self._registered_handlers.copy()


# Event handler registry instance
event_handler_registry = EventHandlerRegistry(domain_event_dispatcher)
