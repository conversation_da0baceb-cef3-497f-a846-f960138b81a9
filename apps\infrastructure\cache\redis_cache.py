"""
Redis Cache Implementation
Provides caching infrastructure using Redis for improved performance.
"""
import json
import pickle
from typing import Any, List, Optional, Union
from datetime import timedelta, datetime
import redis
from django.conf import settings
from apps.domain.shared.interfaces import ICacheService
import logging


logger = logging.getLogger('hospital_management.cache')


class RedisCache(ICacheService):
    """
    Redis-based cache implementation.
    Provides high-performance caching with support for various data types.
    """
    
    def __init__(self):
        """Initialize Redis connection."""
        self._redis_client = None
        self._default_timeout = getattr(settings, 'CACHE_DEFAULT_TIMEOUT', 300)  # 5 minutes
        self._key_prefix = getattr(settings, 'CACHE_KEY_PREFIX', 'hospital_mgmt:')
    
    @property
    def redis_client(self):
        """Get Redis client with lazy initialization."""
        if self._redis_client is None:
            redis_url = getattr(settings, 'REDIS_URL', 'redis://localhost:6379/1')
            self._redis_client = redis.from_url(
                redis_url,
                decode_responses=False,  # We'll handle encoding/decoding manually
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            # Test connection
            try:
                self._redis_client.ping()
                logger.info("Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                raise
        
        return self._redis_client
    
    def _make_key(self, key: str) -> str:
        """Create prefixed cache key."""
        return f"{self._key_prefix}{key}"
    
    def _serialize_value(self, value: Any) -> bytes:
        """Serialize value for Redis storage."""
        try:
            if isinstance(value, (str, int, float, bool)):
                return json.dumps(value).encode('utf-8')
            else:
                # Use pickle for complex objects
                return pickle.dumps(value)
        except Exception as e:
            logger.error(f"Failed to serialize cache value: {str(e)}")
            raise
    
    def _deserialize_value(self, value: bytes) -> Any:
        """Deserialize value from Redis storage."""
        try:
            # Try JSON first (for simple types)
            try:
                return json.loads(value.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Fall back to pickle for complex objects
                return pickle.loads(value)
        except Exception as e:
            logger.error(f"Failed to deserialize cache value: {str(e)}")
            raise
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            cache_key = self._make_key(key)
            value = self.redis_client.get(cache_key)
            
            if value is None:
                logger.debug(f"Cache miss for key: {key}")
                return None
            
            logger.debug(f"Cache hit for key: {key}")
            return self._deserialize_value(value)
            
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {str(e)}")
            return None
    
    async def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            cache_key = self._make_key(key)
            serialized_value = self._serialize_value(value)
            
            if timeout is None:
                timeout = self._default_timeout
            
            result = self.redis_client.setex(cache_key, timeout, serialized_value)
            
            if result:
                logger.debug(f"Cache set for key: {key}, timeout: {timeout}s")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            cache_key = self._make_key(key)
            result = self.redis_client.delete(cache_key)
            
            if result:
                logger.debug(f"Cache delete for key: {key}")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            cache_key = self._make_key(key)
            return bool(self.redis_client.exists(cache_key))
            
        except Exception as e:
            logger.error(f"Cache exists check error for key {key}: {str(e)}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        try:
            cache_pattern = self._make_key(pattern)
            keys = self.redis_client.keys(cache_pattern)
            
            if keys:
                deleted_count = self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted_count} cache keys matching pattern: {pattern}")
                return deleted_count
            
            return 0
            
        except Exception as e:
            logger.error(f"Cache clear pattern error for {pattern}: {str(e)}")
            return 0
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment numeric value in cache."""
        try:
            cache_key = self._make_key(key)
            result = self.redis_client.incrby(cache_key, amount)
            logger.debug(f"Cache increment for key: {key}, amount: {amount}")
            return result
            
        except Exception as e:
            logger.error(f"Cache increment error for key {key}: {str(e)}")
            return 0
    
    async def expire(self, key: str, timeout: int) -> bool:
        """Set expiration for existing key."""
        try:
            cache_key = self._make_key(key)
            result = self.redis_client.expire(cache_key, timeout)
            
            if result:
                logger.debug(f"Cache expire set for key: {key}, timeout: {timeout}s")
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"Cache expire error for key {key}: {str(e)}")
            return False
    
    async def get_ttl(self, key: str) -> int:
        """Get time-to-live for key."""
        try:
            cache_key = self._make_key(key)
            ttl = self.redis_client.ttl(cache_key)
            return ttl if ttl >= 0 else -1
            
        except Exception as e:
            logger.error(f"Cache TTL check error for key {key}: {str(e)}")
            return -1


class CacheManager:
    """
    High-level cache manager with domain-specific caching strategies.
    Provides convenient methods for common caching patterns.
    """
    
    def __init__(self, cache_service: ICacheService):
        self.cache = cache_service
    
    # Patient Cache Methods
    async def get_patient(self, patient_id: int) -> Optional[Any]:
        """Get cached patient data."""
        return await self.cache.get(f"patient:{patient_id}")
    
    async def set_patient(self, patient_id: int, patient_data: Any, timeout: int = 600) -> bool:
        """Cache patient data for 10 minutes."""
        return await self.cache.set(f"patient:{patient_id}", patient_data, timeout)
    
    async def invalidate_patient(self, patient_id: int) -> bool:
        """Invalidate patient cache."""
        return await self.cache.delete(f"patient:{patient_id}")
    
    # Appointment Cache Methods
    async def get_doctor_appointments(self, doctor_id: int, date: str) -> Optional[List]:
        """Get cached doctor appointments for specific date."""
        return await self.cache.get(f"appointments:doctor:{doctor_id}:{date}")
    
    async def set_doctor_appointments(self, doctor_id: int, date: str, 
                                    appointments: List, timeout: int = 1800) -> bool:
        """Cache doctor appointments for 30 minutes."""
        return await self.cache.set(
            f"appointments:doctor:{doctor_id}:{date}", 
            appointments, 
            timeout
        )
    
    async def invalidate_doctor_appointments(self, doctor_id: int, date: str = None) -> int:
        """Invalidate doctor appointment cache."""
        if date:
            await self.cache.delete(f"appointments:doctor:{doctor_id}:{date}")
            return 1
        else:
            return await self.cache.clear_pattern(f"appointments:doctor:{doctor_id}:*")
    
    # Medication Cache Methods
    async def get_medication_stock(self, medication_id: int) -> Optional[dict]:
        """Get cached medication stock info."""
        return await self.cache.get(f"medication:stock:{medication_id}")
    
    async def set_medication_stock(self, medication_id: int, stock_data: dict, 
                                 timeout: int = 300) -> bool:
        """Cache medication stock for 5 minutes."""
        return await self.cache.set(f"medication:stock:{medication_id}", stock_data, timeout)
    
    async def invalidate_medication_stock(self, medication_id: int) -> bool:
        """Invalidate medication stock cache."""
        return await self.cache.delete(f"medication:stock:{medication_id}")
    
    # Room Cache Methods
    async def get_available_rooms(self, department_id: int, room_type: str, date: str) -> Optional[List]:
        """Get cached available rooms."""
        return await self.cache.get(f"rooms:available:{department_id}:{room_type}:{date}")
    
    async def set_available_rooms(self, department_id: int, room_type: str, 
                                date: str, rooms: List, timeout: int = 900) -> bool:
        """Cache available rooms for 15 minutes."""
        return await self.cache.set(
            f"rooms:available:{department_id}:{room_type}:{date}", 
            rooms, 
            timeout
        )
    
    async def invalidate_room_availability(self, department_id: int = None) -> int:
        """Invalidate room availability cache."""
        if department_id:
            return await self.cache.clear_pattern(f"rooms:available:{department_id}:*")
        else:
            return await self.cache.clear_pattern("rooms:available:*")
    
    # Search Cache Methods
    async def get_search_results(self, search_type: str, query_hash: str) -> Optional[dict]:
        """Get cached search results."""
        return await self.cache.get(f"search:{search_type}:{query_hash}")
    
    async def set_search_results(self, search_type: str, query_hash: str, 
                               results: dict, timeout: int = 600) -> bool:
        """Cache search results for 10 minutes."""
        return await self.cache.set(f"search:{search_type}:{query_hash}", results, timeout)
    
    # Statistics Cache Methods
    async def get_dashboard_stats(self, user_role: str, date: str) -> Optional[dict]:
        """Get cached dashboard statistics."""
        return await self.cache.get(f"stats:dashboard:{user_role}:{date}")
    
    async def set_dashboard_stats(self, user_role: str, date: str, 
                                stats: dict, timeout: int = 3600) -> bool:
        """Cache dashboard stats for 1 hour."""
        return await self.cache.set(f"stats:dashboard:{user_role}:{date}", stats, timeout)
    
    # User Session Cache Methods
    async def get_user_session(self, session_id: str) -> Optional[dict]:
        """Get cached user session data."""
        return await self.cache.get(f"session:{session_id}")
    
    async def set_user_session(self, session_id: str, session_data: dict, 
                             timeout: int = 86400) -> bool:
        """Cache user session for 24 hours."""
        return await self.cache.set(f"session:{session_id}", session_data, timeout)
    
    async def invalidate_user_session(self, session_id: str) -> bool:
        """Invalidate user session."""
        return await self.cache.delete(f"session:{session_id}")
    
    # Rate Limiting Methods
    async def check_rate_limit(self, identifier: str, limit: int, window: int) -> bool:
        """Check rate limit for identifier."""
        key = f"rate_limit:{identifier}"
        current_count = await self.cache.increment(key)
        
        if current_count == 1:
            # First request in window, set expiration
            await self.cache.expire(key, window)
        
        return current_count <= limit
    
    async def get_rate_limit_status(self, identifier: str) -> dict:
        """Get current rate limit status."""
        key = f"rate_limit:{identifier}"
        current_count = await self.cache.get(key) or 0
        ttl = await self.cache.get_ttl(key)
        
        return {
            'current_count': current_count,
            'ttl': ttl,
            'reset_time': datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
        }
    
    # Cache Maintenance Methods
    async def clear_all_cache(self) -> int:
        """Clear all application cache."""
        return await self.cache.clear_pattern("*")
    
    async def warm_up_cache(self) -> None:
        """Warm up cache with frequently accessed data."""
        # This would be implemented based on actual usage patterns
        logger.info("Cache warm-up initiated")
        # Example: Pre-load department data, common medications, etc.
        pass


# Cache Decorators
def cache_result(key_func, timeout: int = 300):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            cache = RedisCache()
            cache_key = key_func(*args, **kwargs)
            
            # Try to get from cache first
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


def invalidate_cache(key_func):
    """Decorator to invalidate cache after function execution."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            cache = RedisCache()
            cache_key = key_func(*args, **kwargs)
            await cache.delete(cache_key)
            
            return result
        return wrapper
    return decorator
