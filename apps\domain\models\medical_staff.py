"""
Medical Staff Domain Model
Defines the Medical Staff entity for hospital personnel management.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from apps.domain.models.department import KhoaPhong
from apps.domain.models.user import NguoiDung
from datetime import date
import re


class NhanVienYTe(BaseEntity):
    """
    Medical Staff model for hospital management system.
    Represents doctors, nurses, and other medical personnel.
    """
    
    LOAI_NHAN_VIEN_CHOICES = [
        ('Bac_Si', '<PERSON><PERSON>c sĩ'),
        ('Y_Ta', 'Y tá'),
        ('<PERSON>u_Duong', 'Điều dưỡng'),
        ('<PERSON>y_Thuat_Vien', '<PERSON><PERSON> thuật viên'),
        ('<PERSON>c_Si', '<PERSON><PERSON><PERSON><PERSON> sĩ'),
        ('<PERSON><PERSON>_Vien_Hanh_Chinh', '<PERSON>h<PERSON> viên hành chính'),
    ]
    
    TRANG_THAI_CHOICES = [
        ('Active', '<PERSON>ang làm việc'),
        ('On_Leave', '<PERSON>ang nghỉ phép'),
        ('Suspended', '<PERSON>ạm ngưng'),
        ('Resigned', 'Đã nghỉ việc'),
        ('Retired', '<PERSON><PERSON> về hưu'),
    ]
    
    TRINH_DO_CHOICES = [
        ('Cao_Dang', 'Cao đẳng'),
        ('Dai_Hoc', 'Đại học'),
        ('Thac_Si', 'Thạc sĩ'),
        ('Tien_Si', 'Tiến sĩ'),
        ('Giao_Su', 'Giáo sư'),
        ('Pho_Giao_Su', '<PERSON>ó giáo sư'),
    ]
    
    # Primary Key
    NhanVienID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    nguoi_dung = models.OneToOneField(
        NguoiDung,
        on_delete=models.CASCADE,
        related_name='nhan_vien_y_te',
        verbose_name='Người dùng',
        help_text='Tài khoản người dùng liên kết'
    )
    
    khoa_phong = models.ForeignKey(
        KhoaPhong,
        on_delete=models.PROTECT,
        related_name='nhan_vien_y_te',
        verbose_name='Khoa phòng',
        help_text='Khoa phòng làm việc'
    )
    
    # Basic Information
    ma_nhan_vien = models.CharField(
        'Mã nhân viên',
        max_length=20,
        unique=True,
        help_text='Mã định danh duy nhất của nhân viên'
    )
    
    ho_ten = models.CharField(
        'Họ và tên',
        max_length=100,
        help_text='Họ và tên đầy đủ của nhân viên'
    )
    
    ngay_sinh = models.DateField(
        'Ngày sinh',
        help_text='Ngày sinh của nhân viên'
    )
    
    gioi_tinh = models.CharField(
        'Giới tính',
        max_length=10,
        choices=[
            ('Nam', 'Nam'),
            ('Nữ', 'Nữ'),
            ('Khác', 'Khác'),
        ],
        help_text='Giới tính của nhân viên'
    )
    
    # Professional Information
    loai_nhan_vien = models.CharField(
        'Loại nhân viên',
        max_length=30,
        choices=LOAI_NHAN_VIEN_CHOICES,
        help_text='Loại nhân viên y tế'
    )
    
    chuyen_khoa = models.CharField(
        'Chuyên khoa',
        max_length=100,
        blank=True,
        null=True,
        help_text='Chuyên khoa của bác sĩ (nếu có)'
    )
    
    trinh_do_hoc_van = models.CharField(
        'Trình độ học vấn',
        max_length=20,
        choices=TRINH_DO_CHOICES,
        help_text='Trình độ học vấn cao nhất'
    )
    
    bang_cap = models.TextField(
        'Bằng cấp',
        blank=True,
        null=True,
        help_text='Danh sách bằng cấp và chứng chỉ'
    )
    
    kinh_nghiem_lam_viec = models.IntegerField(
        'Kinh nghiệm làm việc',
        default=0,
        help_text='Số năm kinh nghiệm làm việc'
    )
    
    # Contact Information
    so_dien_thoai = models.CharField(
        'Số điện thoại',
        max_length=15,
        help_text='Số điện thoại liên lạc'
    )
    
    email = models.EmailField(
        'Email',
        help_text='Địa chỉ email công việc'
    )
    
    dia_chi = models.TextField(
        'Địa chỉ',
        help_text='Địa chỉ thường trú'
    )
    
    # Employment Information
    ngay_vao_lam = models.DateField(
        'Ngày vào làm',
        help_text='Ngày bắt đầu làm việc tại bệnh viện'
    )
    
    hop_dong = models.CharField(
        'Loại hợp đồng',
        max_length=30,
        choices=[
            ('Bien_Che', 'Biên chế'),
            ('Hop_Dong', 'Hợp đồng'),
            ('Thoi_Vu', 'Thời vụ'),
            ('Thuc_Tap', 'Thực tập'),
        ],
        default='Hop_Dong',
        help_text='Loại hợp đồng lao động'
    )
    
    luong_co_ban = models.DecimalField(
        'Lương cơ bản',
        max_digits=12,
        decimal_places=0,
        help_text='Mức lương cơ bản (VND)'
    )
    
    # Status and Schedule
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Active',
        help_text='Trạng thái làm việc hiện tại'
    )
    
    ca_lam_viec = models.CharField(
        'Ca làm việc',
        max_length=30,
        choices=[
            ('Sang', 'Ca sáng'),
            ('Chieu', 'Ca chiều'),
            ('Toi', 'Ca tối'),
            ('Dem', 'Ca đêm'),
            ('Full_Time', 'Toàn thời gian'),
        ],
        default='Full_Time',
        help_text='Ca làm việc chính'
    )
    
    # License and Certification
    so_giay_phep_hanh_nghe = models.CharField(
        'Số giấy phép hành nghề',
        max_length=30,
        blank=True,
        null=True,
        help_text='Số giấy phép hành nghề y khoa'
    )
    
    ngay_cap_giay_phep = models.DateField(
        'Ngày cấp giấy phép',
        blank=True,
        null=True,
        help_text='Ngày cấp giấy phép hành nghề'
    )
    
    ngay_het_han_giay_phep = models.DateField(
        'Ngày hết hạn giấy phép',
        blank=True,
        null=True,
        help_text='Ngày hết hạn giấy phép hành nghề'
    )
    
    # Timestamps
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo hồ sơ nhân viên'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật thông tin lần cuối'
    )
    
    class Meta:
        db_table = 'NhanVienYTe'
        verbose_name = 'Nhân viên y tế'
        verbose_name_plural = 'Nhân viên y tế'
        ordering = ['ho_ten']
        indexes = [
            models.Index(fields=['ma_nhan_vien']),
            models.Index(fields=['ho_ten']),
            models.Index(fields=['loai_nhan_vien']),
            models.Index(fields=['chuyen_khoa']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['khoa_phong', 'loai_nhan_vien']),
            models.Index(fields=['so_giay_phep_hanh_nghe']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(kinh_nghiem_lam_viec__gte=0),
                name='check_positive_experience'
            ),
            models.CheckConstraint(
                check=models.Q(luong_co_ban__gt=0),
                name='check_positive_salary'
            ),
            models.CheckConstraint(
                check=models.Q(ngay_sinh__lte=timezone.now().date()),
                name='check_birth_date_not_future'
            ),
        ]
    
    def __str__(self):
        return f"{self.ho_ten} ({self.ma_nhan_vien}) - {self.get_loai_nhan_vien_display()}"
    
    def save(self, *args, **kwargs):
        """Override save to generate staff code and validate data."""
        # Auto-generate staff code if not provided
        if not self.ma_nhan_vien:
            self.ma_nhan_vien = self.generate_staff_code()
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate model fields."""
        # Validate birth date
        if self.ngay_sinh and self.ngay_sinh > date.today():
            raise ValidationError({
                'ngay_sinh': 'Ngày sinh không thể ở tương lai'
            })
        
        # Validate work start date
        if self.ngay_vao_lam and self.ngay_sinh:
            age_at_work = self.ngay_vao_lam.year - self.ngay_sinh.year
            if age_at_work < 18:
                raise ValidationError({
                    'ngay_vao_lam': 'Nhân viên phải từ 18 tuổi trở lên khi vào làm'
                })
        
        # Validate phone number
        if self.so_dien_thoai:
            phone_pattern = r'^[\d\-\+\(\)\s]+$'
            if not re.match(phone_pattern, self.so_dien_thoai):
                raise ValidationError({
                    'so_dien_thoai': 'Số điện thoại không hợp lệ'
                })
        
        # Validate license for doctors
        if self.loai_nhan_vien == 'Bac_Si' and not self.so_giay_phep_hanh_nghe:
            raise ValidationError({
                'so_giay_phep_hanh_nghe': 'Bác sĩ phải có giấy phép hành nghề'
            })
        
        # Validate license expiry
        if self.ngay_het_han_giay_phep and self.ngay_cap_giay_phep:
            if self.ngay_het_han_giay_phep <= self.ngay_cap_giay_phep:
                raise ValidationError({
                    'ngay_het_han_giay_phep': 'Ngày hết hạn phải sau ngày cấp giấy phép'
                })
    
    def generate_staff_code(self) -> str:
        """Generate unique staff code."""
        # Format: Staff type prefix + Department code + sequential number
        type_prefixes = {
            'Bac_Si': 'BS',
            'Y_Ta': 'YT',
            'Dieu_Duong': 'DD',
            'Ky_Thuat_Vien': 'KTV',
            'Duoc_Si': 'DS',
            'Nhan_Vien_Hanh_Chinh': 'HC'
        }
        
        prefix = type_prefixes.get(self.loai_nhan_vien, 'NV')
        dept_code = self.khoa_phong.ma_khoa_phong[:3] if self.khoa_phong else 'GEN'
        
        # Find the highest sequential number for this type and department
        pattern = f"{prefix}{dept_code}"
        existing_codes = NhanVienYTe.objects.filter(
            ma_nhan_vien__startswith=pattern
        ).values_list('ma_nhan_vien', flat=True)
        
        max_seq = 0
        for code in existing_codes:
            try:
                seq = int(code[len(pattern):])
                max_seq = max(max_seq, seq)
            except (ValueError, IndexError):
                continue
        
        return f"{pattern}{max_seq + 1:04d}"
    
    # Business Methods
    def get_age(self) -> int:
        """Calculate staff member's age."""
        if not self.ngay_sinh:
            return 0
        
        today = date.today()
        age = today.year - self.ngay_sinh.year
        
        if today.month < self.ngay_sinh.month or \
           (today.month == self.ngay_sinh.month and today.day < self.ngay_sinh.day):
            age -= 1
        
        return age
    
    def get_years_of_service(self) -> int:
        """Calculate years of service at the hospital."""
        if not self.ngay_vao_lam:
            return 0
        
        today = date.today()
        years = today.year - self.ngay_vao_lam.year
        
        if today.month < self.ngay_vao_lam.month or \
           (today.month == self.ngay_vao_lam.month and today.day < self.ngay_vao_lam.day):
            years -= 1
        
        return years
    
    def is_doctor(self) -> bool:
        """Check if staff member is a doctor."""
        return self.loai_nhan_vien == 'Bac_Si'
    
    def is_nurse(self) -> bool:
        """Check if staff member is a nurse."""
        return self.loai_nhan_vien in ['Y_Ta', 'Dieu_Duong']
    
    def is_pharmacist(self) -> bool:
        """Check if staff member is a pharmacist."""
        return self.loai_nhan_vien == 'Duoc_Si'
    
    def is_active(self) -> bool:
        """Check if staff member is currently active."""
        return self.trang_thai == 'Active'
    
    def has_valid_license(self) -> bool:
        """Check if medical license is valid."""
        if not self.so_giay_phep_hanh_nghe:
            return False
        
        if self.ngay_het_han_giay_phep:
            return self.ngay_het_han_giay_phep > date.today()
        
        return True
    
    def can_prescribe_medication(self) -> bool:
        """Check if staff can prescribe medication."""
        return self.is_doctor() and self.is_active() and self.has_valid_license()
    
    def can_dispense_medication(self) -> bool:
        """Check if staff can dispense medication."""
        return (self.is_pharmacist() or self.is_doctor()) and self.is_active()
    
    def can_treat_patients(self) -> bool:
        """Check if staff can treat patients."""
        return (self.is_doctor() or self.is_nurse()) and self.is_active()
    
    def get_specialties(self) -> list:
        """Get list of medical specialties."""
        if not self.chuyen_khoa:
            return []
        
        return [specialty.strip() for specialty in self.chuyen_khoa.split(',')]
    
    def has_specialty(self, specialty: str) -> bool:
        """Check if staff has specific specialty."""
        specialties = self.get_specialties()
        return any(specialty.lower() in s.lower() for s in specialties)
    
    def get_qualifications(self) -> list:
        """Get list of qualifications and certifications."""
        if not self.bang_cap:
            return []
        
        return [qual.strip() for qual in self.bang_cap.split('\n') if qual.strip()]
    
    def is_senior_staff(self) -> bool:
        """Check if staff is senior (5+ years experience or advanced degree)."""
        return (self.kinh_nghiem_lam_viec >= 5 or 
                self.trinh_do_hoc_van in ['Tien_Si', 'Giao_Su', 'Pho_Giao_Su'])
    
    def get_work_schedule_display(self) -> str:
        """Get formatted work schedule display."""
        schedule_map = {
            'Sang': '06:00 - 14:00',
            'Chieu': '14:00 - 22:00',
            'Toi': '18:00 - 02:00',
            'Dem': '22:00 - 06:00',
            'Full_Time': '08:00 - 17:00'
        }
        
        return schedule_map.get(self.ca_lam_viec, 'Chưa xác định')
    
    def calculate_monthly_salary(self) -> float:
        """Calculate estimated monthly salary."""
        base_salary = float(self.luong_co_ban)
        
        # Add experience bonus (2% per year, max 20%)
        experience_bonus = min(self.kinh_nghiem_lam_viec * 0.02, 0.20)
        
        # Add degree bonus
        degree_bonuses = {
            'Cao_Dang': 0.0,
            'Dai_Hoc': 0.10,
            'Thac_Si': 0.15,
            'Tien_Si': 0.25,
            'Giao_Su': 0.40,
            'Pho_Giao_Su': 0.35
        }
        degree_bonus = degree_bonuses.get(self.trinh_do_hoc_van, 0.0)
        
        # Add position bonus
        position_bonuses = {
            'Bac_Si': 0.30,
            'Y_Ta': 0.10,
            'Dieu_Duong': 0.15,
            'Duoc_Si': 0.20,
            'Ky_Thuat_Vien': 0.05,
            'Nhan_Vien_Hanh_Chinh': 0.0
        }
        position_bonus = position_bonuses.get(self.loai_nhan_vien, 0.0)
        
        total_bonus = experience_bonus + degree_bonus + position_bonus
        return base_salary * (1 + total_bonus)
    
    def set_on_leave(self, leave_type: str = 'Annual'):
        """Set staff member on leave."""
        old_status = self.trang_thai
        self.trang_thai = 'On_Leave'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_status_changed_event(old_status, 'On_Leave', leave_type)
    
    def return_from_leave(self):
        """Return staff member from leave."""
        old_status = self.trang_thai
        self.trang_thai = 'Active'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_status_changed_event(old_status, 'Active')
    
    def suspend_staff(self, reason: str):
        """Suspend staff member."""
        old_status = self.trang_thai
        self.trang_thai = 'Suspended'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_suspended_event(reason)
    
    def resign_staff(self, resignation_date: date = None):
        """Process staff resignation."""
        old_status = self.trang_thai
        self.trang_thai = 'Resigned'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_resigned_event(resignation_date or date.today())
    
    def update_specialty(self, new_specialty: str):
        """Update staff specialty."""
        old_specialty = self.chuyen_khoa
        self.chuyen_khoa = new_specialty
        self.save(update_fields=['chuyen_khoa', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_specialty_changed_event(old_specialty, new_specialty)
    
    def promote_position(self, new_position: str, salary_increase: float = 0):
        """Promote staff to new position."""
        old_position = self.loai_nhan_vien
        old_salary = float(self.luong_co_ban)
        
        self.loai_nhan_vien = new_position
        if salary_increase > 0:
            self.luong_co_ban += salary_increase
        
        # Update staff code to reflect new position
        self.ma_nhan_vien = self.generate_staff_code()
        
        self.save(update_fields=['loai_nhan_vien', 'luong_co_ban', 'ma_nhan_vien', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_staff_promoted_event(old_position, new_position, old_salary, float(self.luong_co_ban))
    
    @property
    def display_name(self) -> str:
        """Get display name with position."""
        return f"{self.get_loai_nhan_vien_display()} {self.ho_ten}"
    
    @property
    def professional_summary(self) -> str:
        """Get professional summary."""
        summary_parts = [
            self.get_loai_nhan_vien_display(),
            f"{self.kinh_nghiem_lam_viec} năm kinh nghiệm"
        ]
        
        if self.chuyen_khoa:
            summary_parts.append(f"Chuyên khoa: {self.chuyen_khoa}")
        
        return " - ".join(summary_parts)
    
    @property
    def license_status(self) -> str:
        """Get license status display."""
        if not self.so_giay_phep_hanh_nghe:
            return 'Chưa có giấy phép'
        
        if self.has_valid_license():
            return 'Giấy phép hợp lệ'
        else:
            return 'Giấy phép hết hạn'
    
    # Domain Events
    def raise_staff_hired_event(self):
        """Raise staff hired domain event."""
        from apps.domain.events.domain_events import MedicalStaffHiredEvent
        event = MedicalStaffHiredEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            staff_type=self.loai_nhan_vien,
            department_id=self.khoa_phong.KhoaPhongID,
            hired_date=self.ngay_vao_lam,
            hired_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_staff_status_changed_event(self, old_status: str, new_status: str, reason: str = None):
        """Raise staff status changed domain event."""
        from apps.domain.events.domain_events import MedicalStaffStatusChangedEvent
        event = MedicalStaffStatusChangedEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            old_status=old_status,
            new_status=new_status,
            reason=reason,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_staff_suspended_event(self, reason: str):
        """Raise staff suspended domain event."""
        from apps.domain.events.domain_events import MedicalStaffSuspendedEvent
        event = MedicalStaffSuspendedEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            suspension_reason=reason,
            suspended_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_staff_resigned_event(self, resignation_date: date):
        """Raise staff resigned domain event."""
        from apps.domain.events.domain_events import MedicalStaffResignedEvent
        event = MedicalStaffResignedEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            resignation_date=resignation_date,
            resigned_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_staff_specialty_changed_event(self, old_specialty: str, new_specialty: str):
        """Raise staff specialty changed domain event."""
        from apps.domain.events.domain_events import MedicalStaffSpecialtyChangedEvent
        event = MedicalStaffSpecialtyChangedEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            old_specialty=old_specialty,
            new_specialty=new_specialty,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_staff_promoted_event(self, old_position: str, new_position: str, old_salary: float, new_salary: float):
        """Raise staff promoted domain event."""
        from apps.domain.events.domain_events import MedicalStaffPromotedEvent
        event = MedicalStaffPromotedEvent(
            staff_id=self.NhanVienID,
            staff_code=self.ma_nhan_vien,
            staff_name=self.ho_ten,
            old_position=old_position,
            new_position=new_position,
            old_salary=old_salary,
            new_salary=new_salary,
            promoted_at=timezone.now()
        )
        self.add_domain_event(event)
