# Hospital Management System - Domain Analysis

## Core Entities

### 1. **<PERSON><PERSON><PERSON> (Patient)**
- <PERSON><PERSON><PERSON><PERSON> lý thông tin cá nhân bệnh nhân
- <PERSON><PERSON><PERSON> sử khám bệnh
- <PERSON><PERSON> sơ bệnh án
- Thông tin bảo hiểm y tế

### 2. **<PERSON><PERSON><PERSON> (Doctor)**
- Thông tin cá nhân và chuyên môn
- <PERSON><PERSON><PERSON><PERSON> khoa
- <PERSON><PERSON>ch làm việc
- <PERSON><PERSON> sách bệnh nhân đang điều trị

### 3. **<PERSON> <PERSON><PERSON> (Nurse)**
- Thông tin cá nhân
- Ca làm việc
- Khoa phòng phụ trách
- Nhiệm vụ chăm sóc bệnh nhân

### 4. **<PERSON><PERSON><PERSON> (Department)**
- Tên khoa
- <PERSON><PERSON><PERSON>ho<PERSON> (Nội khoa, Ngo<PERSON><PERSON>hoa, <PERSON><PERSON><PERSON> khoa, <PERSON><PERSON> khoa, etc.)
- Trưởng khoa
- <PERSON><PERSON> sách nhân viên

### 5. **<PERSON><PERSON><PERSON> (Room)**
- <PERSON><PERSON> phò<PERSON>
- <PERSON><PERSON><PERSON> (<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ICU)
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON><PERSON>á<PERSON> (Trống, Đ<PERSON> đầy)
- Giá phòng/ngày

### 6. **Lịch Hẹn (Appointment)**
- Bệnh nhân
- Bác sĩ
- Thời gian hẹn
- Lý do khám
- Trạng thái (Đã đặt, Đã khám, Hủy)

### 7. **Thuốc (Medication)**
- Tên thuốc
- Loại thuốc
- Đơn vị tính
- Số lượng tồn kho
- Giá bán
- Ngày hết hạn

### 8. **Đơn Thuốc (Prescription)**
- Bác sĩ kê đơn
- Bệnh nhân
- Danh sách thuốc
- Liều lượng
- Hướng dẫn sử dụng

### 9. **Hóa Đơn (Bill)**
- Bệnh nhân
- Tổng tiền
- Chi tiết các khoản phí
- Trạng thái thanh toán
- Phương thức thanh toán

### 10. **Người Dùng (User)**
- Tài khoản đăng nhập
- Vai trò (Admin, Bác sĩ, Y tá, Lễ tân, Kế toán)
- Quyền hạn

## Business Rules & Constraints

### Quy tắc nghiệp vụ:

1. **Lịch hẹn:**
   - Một bác sĩ không thể có 2 lịch hẹn cùng thời gian
   - Lịch hẹn phải trong giờ làm việc của bác sĩ
   - Bệnh nhân phải đăng ký trước tối thiểu 30 phút

2. **Phòng bệnh:**
   - Không vượt quá sức chứa tối đa
   - Phòng ICU cần có bác sĩ trực 24/7
   - Giá phòng VIP cao hơn phòng thường 3-5 lần

3. **Kê đơn thuốc:**
   - Chỉ bác sĩ mới có quyền kê đơn
   - Không kê thuốc hết hạn
   - Kiểm tra tương tác thuốc

4. **Thanh toán:**
   - Hóa đơn phải được tạo trước khi xuất viện
   - Hỗ trợ thanh toán bảo hiểm y tế
   - Lưu lịch sử thanh toán

5. **Bảo mật:**
   - Thông tin bệnh án là bí mật y tế
   - Phân quyền truy cập theo vai trò
   - Log mọi thao tác quan trọng

### Ràng buộc hệ thống:

1. **Dữ liệu:**
   - Tối đa 10 bảng trong database
   - Sử dụng tiếng Việt có dấu
   - Tiền tệ VND

2. **Hiệu năng:**
   - Response time < 2 giây
   - Hỗ trợ 100+ concurrent users
   - Backup dữ liệu hàng ngày

3. **Tích hợp:**
   - API RESTful
   - JWT Authentication
   - Swagger documentation
