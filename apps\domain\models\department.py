"""
Department Domain Model
Defines the Department entity for hospital organizational structure.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity


class KhoaPhong(BaseEntity):
    """
    Department model for hospital management system.
    Represents hospital departments/divisions.
    """
    
    # Primary Key
    KhoaPhongID = models.AutoField(primary_key=True)
    
    # Basic information
    ten_khoa_phong = models.CharField(
        'Tên khoa phòng',
        max_length=100,
        unique=True,
        help_text='Tên đầy đủ của khoa phòng'
    )
    
    ma_khoa_phong = models.Char<PERSON>ield(
        'Mã khoa phòng',
        max_length=20,
        unique=True,
        help_text='Mã định danh duy nhất của khoa phòng'
    )
    
    mo_ta = models.TextField(
        'Mô tả',
        blank=True,
        null=True,
        help_text='Mô tả chi tiết về khoa phòng'
    )
    
    # Location information
    tang = models.IntegerField(
        'Tầng',
        help_text='Tầng của khoa phòng trong bệnh viện'
    )
    
    toa_nha = models.CharField(
        'Tòa nhà',
        max_length=50,
        default='Tòa A',
        help_text='Tòa nhà chứa khoa phòng'
    )
    
    # Contact information
    so_dien_thoai = models.CharField(
        'Số điện thoại',
        max_length=15,
        blank=True,
        null=True,
        help_text='Số điện thoại liên lạc khoa phòng'
    )
    
    email = models.EmailField(
        'Email',
        blank=True,
        null=True,
        help_text='Địa chỉ email khoa phòng'
    )
    
    # Management information
    truong_khoa = models.CharField(
        'Trưởng khoa',
        max_length=100,
        blank=True,
        null=True,
        help_text='Tên trưởng khoa hiện tại'
    )
    
    # Capacity and resources
    so_giuong = models.IntegerField(
        'Số giường',
        default=0,
        help_text='Tổng số giường của khoa phòng'
    )
    
    so_phong = models.IntegerField(
        'Số phòng',
        default=0,
        help_text='Tổng số phòng của khoa phòng'
    )
    
    # Operating hours
    gio_mo_cua = models.TimeField(
        'Giờ mở cửa',
        default='07:00:00',
        help_text='Giờ bắt đầu hoạt động'
    )
    
    gio_dong_cua = models.TimeField(
        'Giờ đóng cửa',
        default='17:00:00',
        help_text='Giờ kết thúc hoạt động'
    )
    
    # Status
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=[
            ('Active', 'Hoạt động'),
            ('Inactive', 'Tạm ngừng'),
            ('Maintenance', 'Bảo trì'),
            ('Closed', 'Đóng cửa'),
        ],
        default='Active',
        help_text='Trạng thái hoạt động của khoa phòng'
    )
    
    # Timestamps
    ngay_thanh_lap = models.DateField(
        'Ngày thành lập',
        default=timezone.now,
        help_text='Ngày thành lập khoa phòng'
    )
    
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo bản ghi'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật lần cuối'
    )
    
    class Meta:
        db_table = 'KhoaPhong'
        verbose_name = 'Khoa phòng'
        verbose_name_plural = 'Khoa phòng'
        ordering = ['ten_khoa_phong']
        indexes = [
            models.Index(fields=['ma_khoa_phong']),
            models.Index(fields=['ten_khoa_phong']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['tang', 'toa_nha']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(so_giuong__gte=0),
                name='check_positive_bed_count'
            ),
            models.CheckConstraint(
                check=models.Q(so_phong__gte=0),
                name='check_positive_room_count'
            ),
            models.CheckConstraint(
                check=models.Q(tang__gte=0),
                name='check_positive_floor'
            ),
        ]
    
    def __str__(self):
        return f"{self.ten_khoa_phong} ({self.ma_khoa_phong})"
    
    def save(self, *args, **kwargs):
        """Override save to update timestamps and validate data."""
        self.ngay_cap_nhat = timezone.now()
        
        # Auto-generate department code if not provided
        if not self.ma_khoa_phong:
            self.ma_khoa_phong = self.generate_department_code()
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate model fields."""
        # Validate operating hours
        if self.gio_mo_cua and self.gio_dong_cua:
            if self.gio_mo_cua >= self.gio_dong_cua:
                raise ValidationError({
                    'gio_dong_cua': 'Giờ đóng cửa phải sau giờ mở cửa'
                })
        
        # Validate phone number
        if self.so_dien_thoai:
            import re
            phone_pattern = r'^[\d\-\+\(\)\s]+$'
            if not re.match(phone_pattern, self.so_dien_thoai):
                raise ValidationError({
                    'so_dien_thoai': 'Số điện thoại không hợp lệ'
                })
        
        # Validate email
        if self.email and '@' not in self.email:
            raise ValidationError({
                'email': 'Địa chỉ email không hợp lệ'
            })
    
    def generate_department_code(self) -> str:
        """Generate unique department code."""
        import re
        
        # Remove Vietnamese diacritics and create acronym
        name_parts = re.sub(r'[^\w\s]', '', self.ten_khoa_phong).split()
        acronym = ''.join([part[0].upper() for part in name_parts if part])
        
        # Add floor number
        code = f"{acronym}{self.tang:02d}"
        
        # Ensure uniqueness
        counter = 1
        original_code = code
        while KhoaPhong.objects.filter(ma_khoa_phong=code).exclude(pk=self.pk).exists():
            code = f"{original_code}{counter:02d}"
            counter += 1
        
        return code
    
    # Business Methods
    def is_active(self) -> bool:
        """Check if department is currently active."""
        return self.trang_thai == 'Active'
    
    def is_operational(self) -> bool:
        """Check if department is operational (not closed or maintenance)."""
        return self.trang_thai not in ['Closed', 'Maintenance']
    
    def get_current_occupancy_rate(self) -> float:
        """Calculate current occupancy rate."""
        if self.so_giuong == 0:
            return 0.0
        
        # This would need to be implemented with actual room/bed occupancy data
        occupied_beds = 0  # Placeholder - would query related room data
        return (occupied_beds / self.so_giuong) * 100
    
    def get_staff_count(self) -> int:
        """Get total staff count in department."""
        # This would need to be implemented with actual staff relationships
        return self.nhanvienyte_set.count() if hasattr(self, 'nhanvienyte_set') else 0
    
    def get_available_rooms(self) -> int:
        """Get count of available rooms."""
        # This would need to be implemented with actual room relationships
        if hasattr(self, 'phongbenh_set'):
            return self.phongbenh_set.filter(trang_thai='Available').count()
        return 0
    
    def get_doctor_count(self) -> int:
        """Get count of doctors in department."""
        if hasattr(self, 'nhanvienyte_set'):
            return self.nhanvienyte_set.filter(loai_nhan_vien='Bac_Si').count()
        return 0
    
    def get_nurse_count(self) -> int:
        """Get count of nurses in department."""
        if hasattr(self, 'nhanvienyte_set'):
            return self.nhanvienyte_set.filter(loai_nhan_vien='Y_Ta').count()
        return 0
    
    def is_open_now(self) -> bool:
        """Check if department is currently open."""
        if not self.is_operational():
            return False
        
        from datetime import time
        current_time = timezone.localtime().time()
        
        return self.gio_mo_cua <= current_time <= self.gio_dong_cua
    
    def get_operating_hours_display(self) -> str:
        """Get formatted operating hours."""
        return f"{self.gio_mo_cua.strftime('%H:%M')} - {self.gio_dong_cua.strftime('%H:%M')}"
    
    def get_location_display(self) -> str:
        """Get formatted location display."""
        return f"{self.toa_nha}, Tầng {self.tang}"
    
    def activate(self):
        """Activate department."""
        old_status = self.trang_thai
        self.trang_thai = 'Active'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_department_status_changed_event(old_status, 'Active')
    
    def deactivate(self):
        """Deactivate department."""
        old_status = self.trang_thai
        self.trang_thai = 'Inactive'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_department_status_changed_event(old_status, 'Inactive')
    
    def set_maintenance_mode(self):
        """Set department to maintenance mode."""
        old_status = self.trang_thai
        self.trang_thai = 'Maintenance'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_department_status_changed_event(old_status, 'Maintenance')
    
    def update_capacity(self, bed_count: int, room_count: int):
        """Update department capacity."""
        if bed_count < 0 or room_count < 0:
            raise ValueError("Capacity values cannot be negative")
        
        old_bed_count = self.so_giuong
        old_room_count = self.so_phong
        
        self.so_giuong = bed_count
        self.so_phong = room_count
        self.save(update_fields=['so_giuong', 'so_phong', 'ngay_cap_nhat'])
        
        # Raise domain event if capacity changed significantly
        if abs(old_bed_count - bed_count) > 0 or abs(old_room_count - room_count) > 0:
            self.raise_department_capacity_changed_event(
                old_bed_count, bed_count, old_room_count, room_count
            )
    
    def update_operating_hours(self, open_time: str, close_time: str):
        """Update department operating hours."""
        from datetime import time
        
        try:
            new_open_time = time.fromisoformat(open_time)
            new_close_time = time.fromisoformat(close_time)
            
            if new_open_time >= new_close_time:
                raise ValueError("Closing time must be after opening time")
            
            self.gio_mo_cua = new_open_time
            self.gio_dong_cua = new_close_time
            self.save(update_fields=['gio_mo_cua', 'gio_dong_cua', 'ngay_cap_nhat'])
            
        except ValueError as e:
            raise ValidationError(f"Invalid time format: {str(e)}")
    
    @property
    def capacity_utilization(self) -> dict:
        """Get capacity utilization statistics."""
        return {
            'total_beds': self.so_giuong,
            'total_rooms': self.so_phong,
            'occupied_beds': 0,  # Would be calculated from actual data
            'available_rooms': self.get_available_rooms(),
            'occupancy_rate': self.get_current_occupancy_rate(),
            'staff_count': self.get_staff_count()
        }
    
    @property
    def contact_info(self) -> dict:
        """Get contact information."""
        return {
            'phone': self.so_dien_thoai,
            'email': self.email,
            'location': self.get_location_display(),
            'head_of_department': self.truong_khoa
        }
    
    # Domain Events
    def raise_department_created_event(self):
        """Raise department created domain event."""
        from apps.domain.events.domain_events import DepartmentCreatedEvent
        event = DepartmentCreatedEvent(
            department_id=self.KhoaPhongID,
            department_name=self.ten_khoa_phong,
            department_code=self.ma_khoa_phong,
            floor=self.tang,
            building=self.toa_nha,
            created_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_department_status_changed_event(self, old_status: str, new_status: str):
        """Raise department status changed domain event."""
        from apps.domain.events.domain_events import DepartmentStatusChangedEvent
        event = DepartmentStatusChangedEvent(
            department_id=self.KhoaPhongID,
            department_name=self.ten_khoa_phong,
            old_status=old_status,
            new_status=new_status,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_department_capacity_changed_event(self, old_bed_count: int, new_bed_count: int,
                                              old_room_count: int, new_room_count: int):
        """Raise department capacity changed domain event."""
        from apps.domain.events.domain_events import DepartmentCapacityChangedEvent
        event = DepartmentCapacityChangedEvent(
            department_id=self.KhoaPhongID,
            department_name=self.ten_khoa_phong,
            old_bed_count=old_bed_count,
            new_bed_count=new_bed_count,
            old_room_count=old_room_count,
            new_room_count=new_room_count,
            changed_at=timezone.now()
        )
        self.add_domain_event(event)
