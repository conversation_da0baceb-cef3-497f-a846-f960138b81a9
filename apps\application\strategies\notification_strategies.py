"""
Notification Strategy Implementations
Implements Strategy pattern for different notification channels.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List
from datetime import datetime


class INotificationStrategy(ABC):
    """Abstract notification strategy interface."""
    
    @abstractmethod
    async def send_notification(self, recipient: str, subject: str, content: str, **kwargs) -> Dict[str, Any]:
        """Send notification using specific strategy."""
        pass
    
    @abstractmethod
    async def validate_recipient(self, recipient: str) -> bool:
        """Validate recipient format for this strategy."""
        pass
    
    @abstractmethod
    def get_notification_type(self) -> str:
        """Get notification type identifier."""
        pass


class EmailNotificationStrategy(INotificationStrategy):
    """Email notification strategy implementation."""
    
    async def send_notification(self, recipient: str, subject: str, content: str, **kwargs) -> Dict[str, Any]:
        """Send email notification."""
        # In real implementation, integrate with email service (SendGrid, AWS SES, etc.)
        
        return {
            "success": True,
            "notification_id": f"EMAIL_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "type": "email",
            "recipient": recipient,
            "subject": subject,
            "sent_at": datetime.now().isoformat(),
            "status": "sent"
        }
    
    async def validate_recipient(self, recipient: str) -> bool:
        """Validate email format."""
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(email_pattern, recipient) is not None
    
    def get_notification_type(self) -> str:
        return "EMAIL"


class SMSNotificationStrategy(INotificationStrategy):
    """SMS notification strategy implementation."""
    
    async def send_notification(self, recipient: str, subject: str, content: str, **kwargs) -> Dict[str, Any]:
        """Send SMS notification."""
        # In real implementation, integrate with SMS service (Twilio, AWS SNS, etc.)
        
        # SMS combines subject and content
        message = f"{subject}: {content}" if subject else content
        
        return {
            "success": True,
            "notification_id": f"SMS_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "type": "sms",
            "recipient": recipient,
            "message": message[:160],  # SMS length limit
            "sent_at": datetime.now().isoformat(),
            "status": "sent"
        }
    
    async def validate_recipient(self, recipient: str) -> bool:
        """Validate phone number format."""
        import re
        # Vietnamese phone number pattern
        phone_pattern = r'^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$'
        return re.match(phone_pattern, recipient) is not None
    
    def get_notification_type(self) -> str:
        return "SMS"


class PushNotificationStrategy(INotificationStrategy):
    """Push notification strategy implementation."""
    
    async def send_notification(self, recipient: str, subject: str, content: str, **kwargs) -> Dict[str, Any]:
        """Send push notification."""
        # In real implementation, integrate with push service (Firebase, OneSignal, etc.)
        
        return {
            "success": True,
            "notification_id": f"PUSH_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "type": "push",
            "recipient": recipient,  # user_id or device_token
            "title": subject,
            "body": content,
            "sent_at": datetime.now().isoformat(),
            "status": "sent",
            "additional_data": kwargs.get('data', {})
        }
    
    async def validate_recipient(self, recipient: str) -> bool:
        """Validate user ID or device token format."""
        return recipient and len(recipient) > 0
    
    def get_notification_type(self) -> str:
        return "PUSH"


class InAppNotificationStrategy(INotificationStrategy):
    """In-app notification strategy implementation."""
    
    async def send_notification(self, recipient: str, subject: str, content: str, **kwargs) -> Dict[str, Any]:
        """Send in-app notification."""
        # Store notification in database for user to see in app
        
        return {
            "success": True,
            "notification_id": f"APP_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "type": "in_app",
            "recipient": recipient,  # user_id
            "title": subject,
            "message": content,
            "sent_at": datetime.now().isoformat(),
            "status": "delivered",
            "read": False,
            "priority": kwargs.get('priority', 'normal')
        }
    
    async def validate_recipient(self, recipient: str) -> bool:
        """Validate user ID format."""
        try:
            int(recipient)
            return True
        except ValueError:
            return False
    
    def get_notification_type(self) -> str:
        return "IN_APP"


class NotificationStrategyContext:
    """
    Notification strategy context for managing different notification channels.
    Implements Strategy pattern context with Observer pattern for event handling.
    """
    
    def __init__(self):
        self._strategies: Dict[str, INotificationStrategy] = {
            "EMAIL": EmailNotificationStrategy(),
            "SMS": SMSNotificationStrategy(),
            "PUSH": PushNotificationStrategy(),
            "IN_APP": InAppNotificationStrategy()
        }
    
    def add_strategy(self, notification_type: str, strategy: INotificationStrategy) -> None:
        """Add new notification strategy."""
        self._strategies[notification_type] = strategy
    
    def get_strategy(self, notification_type: str) -> INotificationStrategy:
        """Get notification strategy by type."""
        if notification_type not in self._strategies:
            raise ValueError(f"Unsupported notification type: {notification_type}")
        return self._strategies[notification_type]
    
    async def send_notification(
        self, 
        notification_type: str, 
        recipient: str, 
        subject: str, 
        content: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """Send notification using appropriate strategy."""
        strategy = self.get_strategy(notification_type)
        
        # Validate recipient first
        if not await strategy.validate_recipient(recipient):
            return {
                "success": False,
                "message": f"Định dạng người nhận không hợp lệ cho {notification_type}",
                "error_code": "INVALID_RECIPIENT"
            }
        
        return await strategy.send_notification(recipient, subject, content, **kwargs)
    
    async def send_multi_channel_notification(
        self,
        channels: List[str],
        recipient_map: Dict[str, str],
        subject: str,
        content: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Send notification through multiple channels."""
        results = {}
        
        for channel in channels:
            if channel in recipient_map:
                recipient = recipient_map[channel]
                result = await self.send_notification(channel, recipient, subject, content, **kwargs)
                results[channel] = result
        
        return {
            "success": True,
            "message": "Multi-channel notification sent",
            "results": results
        }
    
    def get_supported_notification_types(self) -> List[str]:
        """Get list of supported notification types."""
        return list(self._strategies.keys())


# Notification templates for common hospital scenarios
class NotificationTemplates:
    """Predefined notification templates for hospital events."""
    
    @staticmethod
    def appointment_confirmation(patient_name: str, doctor_name: str, appointment_time: str) -> Dict[str, str]:
        """Appointment confirmation template."""
        return {
            "subject": "Xác nhận lịch hẹn khám bệnh",
            "content": f"Xin chào {patient_name},\n\n"
                      f"Lịch hẹn của bạn đã được xác nhận:\n"
                      f"- Bác sĩ: {doctor_name}\n"
                      f"- Thời gian: {appointment_time}\n\n"
                      f"Vui lòng đến đúng giờ. Cảm ơn!"
        }
    
    @staticmethod
    def appointment_reminder(patient_name: str, doctor_name: str, appointment_time: str) -> Dict[str, str]:
        """Appointment reminder template."""
        return {
            "subject": "Nhắc nhở lịch hẹn khám bệnh",
            "content": f"Xin chào {patient_name},\n\n"
                      f"Nhắc nhở lịch hẹn của bạn:\n"
                      f"- Bác sĩ: {doctor_name}\n"
                      f"- Thời gian: {appointment_time}\n\n"
                      f"Vui lòng đến đúng giờ."
        }
    
    @staticmethod
    def prescription_ready(patient_name: str, prescription_id: str) -> Dict[str, str]:
        """Prescription ready template."""
        return {
            "subject": "Đơn thuốc đã sẵn sàng",
            "content": f"Xin chào {patient_name},\n\n"
                      f"Đơn thuốc #{prescription_id} của bạn đã sẵn sàng để lấy.\n"
                      f"Vui lòng đến quầy thuốc để nhận."
        }
    
    @staticmethod
    def payment_confirmation(patient_name: str, amount: float, payment_method: str) -> Dict[str, str]:
        """Payment confirmation template."""
        return {
            "subject": "Xác nhận thanh toán",
            "content": f"Xin chào {patient_name},\n\n"
                      f"Thanh toán của bạn đã được xử lý thành công:\n"
                      f"- Số tiền: {amount:,.0f} VND\n"
                      f"- Phương thức: {payment_method}\n\n"
                      f"Cảm ơn bạn đã sử dụng dịch vụ!"
        }
    
    @staticmethod
    def test_result_ready(patient_name: str, test_name: str) -> Dict[str, str]:
        """Test result ready template."""
        return {
            "subject": "Kết quả xét nghiệm đã có",
            "content": f"Xin chào {patient_name},\n\n"
                      f"Kết quả xét nghiệm {test_name} của bạn đã có.\n"
                      f"Vui lòng liên hệ bác sĩ để tư vấn kết quả."
        }
