"""
DRF Serializers for Hospital Management API
Handles serialization and deserialization of domain models for REST API endpoints.
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from decimal import Decimal
from apps.domain.models.user import <PERSON>uoi<PERSON>ung
from apps.domain.models.department import <PERSON><PERSON><PERSON><PERSON>hong
from apps.domain.models.patient import <PERSON><PERSON><PERSON><PERSON>
from apps.domain.models.medical_staff import NhanVienYTe
from apps.domain.models.room import PhongBenh
from apps.domain.models.appointment import LichHen
from apps.domain.models.medication import Thuoc
from apps.domain.models.prescription import DonThuoc, ChiTietDonThuoc
from apps.domain.models.bill import HoaDon


# Base Serializers
class BaseModelSerializer(serializers.ModelSerializer):
    """Base serializer with common fields and methods."""
    
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)
    
    def validate_phone_number(self, value):
        """Validate Vietnamese phone number format."""
        if value and not value.startswith(('0', '+84')):
            raise serializers.ValidationError("<PERSON><PERSON> điện tho<PERSON>i phải bắt đầu bằng 0 hoặc +84")
        return value


# Authentication Serializers
class LoginSerializer(serializers.Serializer):
    """User login serializer."""
    
    username = serializers.CharField(max_length=150)
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate(self, data):
        username = data.get('username')
        password = data.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError("Thông tin đăng nhập không hợp lệ")
            if not user.is_active:
                raise serializers.ValidationError("Tài khoản đã bị khóa")
            
            data['user'] = user
            return data
        
        raise serializers.ValidationError("Vui lòng nhập đầy đủ tài khoản và mật khẩu")


class ChangePasswordSerializer(serializers.Serializer):
    """Change password serializer."""
    
    old_password = serializers.CharField(style={'input_type': 'password'})
    new_password = serializers.CharField(style={'input_type': 'password'}, min_length=8)
    confirm_password = serializers.CharField(style={'input_type': 'password'})
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Mật khẩu cũ không đúng")
        return value
    
    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError("Mật khẩu mới không khớp")
        return data


# User Serializers
class UserSerializer(BaseModelSerializer):
    """User serializer for general use."""
    
    password = serializers.CharField(write_only=True, min_length=8)
    confirm_password = serializers.CharField(write_only=True)
    full_name = serializers.CharField(source='ho_ten', read_only=True)
    
    class Meta:
        model = NguoiDung
        fields = [
            'NguoiDungID', 'username', 'email', 'password', 'confirm_password',
            'ho_ten', 'full_name', 'so_dien_thoai', 'dia_chi', 'ngay_sinh',
            'gioi_tinh', 'vai_tro', 'trang_thai', 'avatar', 'is_active',
            'date_joined', 'last_login', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'NguoiDungID': {'read_only': True},
            'date_joined': {'read_only': True},
            'last_login': {'read_only': True}
        }
    
    def validate(self, data):
        if 'password' in data and 'confirm_password' in data:
            if data['password'] != data['confirm_password']:
                raise serializers.ValidationError("Mật khẩu không khớp")
        return data
    
    def create(self, validated_data):
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password')
        user = NguoiDung.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserProfileSerializer(BaseModelSerializer):
    """User profile serializer (read-only)."""
    
    full_name = serializers.CharField(source='ho_ten', read_only=True)
    role_display = serializers.CharField(source='get_vai_tro_display', read_only=True)
    status_display = serializers.CharField(source='get_trang_thai_display', read_only=True)
    
    class Meta:
        model = NguoiDung
        fields = [
            'NguoiDungID', 'username', 'email', 'ho_ten', 'full_name',
            'so_dien_thoai', 'dia_chi', 'ngay_sinh', 'gioi_tinh',
            'vai_tro', 'role_display', 'trang_thai', 'status_display',
            'avatar', 'last_login', 'date_joined'
        ]
        read_only_fields = '__all__'


# Department Serializers
class DepartmentSerializer(BaseModelSerializer):
    """Department serializer."""
    
    staff_count = serializers.IntegerField(read_only=True)
    room_count = serializers.IntegerInteger(read_only=True)
    
    class Meta:
        model = KhoaPhong
        fields = [
            'KhoaPhongID', 'ten_khoa_phong', 'mo_ta', 'vi_tri', 'tang',
            'dien_thoai', 'email', 'truong_khoa', 'so_giuong',
            'so_phong', 'trang_thai', 'gio_mo_cua', 'gio_dong_cua',
            'ghi_chu', 'staff_count', 'room_count', 'created_at', 'updated_at'
        ]


class DepartmentListSerializer(serializers.ModelSerializer):
    """Simplified department serializer for lists."""
    
    class Meta:
        model = KhoaPhong
        fields = ['KhoaPhongID', 'ten_khoa_phong', 'trang_thai']


# Patient Serializers
class PatientSerializer(BaseModelSerializer):
    """Patient serializer."""
    
    age = serializers.IntegerField(read_only=True)
    insurance_display = serializers.CharField(source='get_loai_bao_hiem_display', read_only=True)
    
    class Meta:
        model = BenhNhan
        fields = [
            'BenhNhanID', 'ma_benh_nhan', 'ho_ten', 'ngay_sinh', 'age',
            'gioi_tinh', 'so_dien_thoai', 'email', 'dia_chi', 'so_cccd',
            'nghe_nghiep', 'dan_toc', 'ton_giao', 'so_bao_hiem',
            'loai_bao_hiem', 'insurance_display', 'nguoi_lien_he',
            'sdt_nguoi_lien_he', 'tien_su_benh', 'di_ung',
            'trang_thai', 'ghi_chu', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_benh_nhan': {'read_only': True}
        }


class PatientCreateSerializer(BaseModelSerializer):
    """Patient creation serializer."""
    
    class Meta:
        model = BenhNhan
        fields = [
            'ho_ten', 'ngay_sinh', 'gioi_tinh', 'so_dien_thoai',
            'email', 'dia_chi', 'so_cccd', 'nghe_nghiep',
            'dan_toc', 'ton_giao', 'so_bao_hiem', 'loai_bao_hiem',
            'nguoi_lien_he', 'sdt_nguoi_lien_he', 'tien_su_benh',
            'di_ung', 'ghi_chu'
        ]


class PatientListSerializer(serializers.ModelSerializer):
    """Simplified patient serializer for lists."""
    
    age = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = BenhNhan
        fields = [
            'BenhNhanID', 'ma_benh_nhan', 'ho_ten', 'ngay_sinh',
            'age', 'so_dien_thoai', 'trang_thai'
        ]


# Medical Staff Serializers
class MedicalStaffSerializer(BaseModelSerializer):
    """Medical staff serializer."""
    
    experience_years = serializers.IntegerField(read_only=True)
    department_name = serializers.CharField(source='khoa_phong.ten_khoa_phong', read_only=True)
    
    class Meta:
        model = NhanVienYTe
        fields = [
            'NhanVienID', 'ma_nhan_vien', 'ho_ten', 'chuc_vu',
            'loai_nhan_vien', 'khoa_phong', 'department_name',
            'trinh_do', 'chuyen_khoa', 'kinh_nghiem', 'experience_years',
            'so_dien_thoai', 'email', 'dia_chi', 'ngay_sinh',
            'gioi_tinh', 'so_cccd', 'so_giay_phep', 'ngay_cap_giay_phep',
            'noi_cap_giay_phep', 'ngay_bat_dau', 'luong_co_ban',
            'trang_thai', 'ghi_chu', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_nhan_vien': {'read_only': True}
        }


class MedicalStaffListSerializer(serializers.ModelSerializer):
    """Simplified medical staff serializer for lists."""
    
    department_name = serializers.CharField(source='khoa_phong.ten_khoa_phong', read_only=True)
    
    class Meta:
        model = NhanVienYTe
        fields = [
            'NhanVienID', 'ma_nhan_vien', 'ho_ten', 'chuc_vu',
            'loai_nhan_vien', 'department_name', 'trang_thai'
        ]


# Room Serializers
class RoomSerializer(BaseModelSerializer):
    """Room serializer."""
    
    department_name = serializers.CharField(source='khoa_phong.ten_khoa_phong', read_only=True)
    occupancy_rate = serializers.FloatField(read_only=True)
    
    class Meta:
        model = PhongBenh
        fields = [
            'PhongID', 'ma_phong', 'ten_phong', 'loai_phong',
            'khoa_phong', 'department_name', 'tang', 'so_giuong',
            'so_giuong_trong', 'occupancy_rate', 'thiet_bi',
            'gia_phong', 'trang_thai', 'ghi_chu',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_phong': {'read_only': True}
        }


class RoomListSerializer(serializers.ModelSerializer):
    """Simplified room serializer for lists."""
    
    department_name = serializers.CharField(source='khoa_phong.ten_khoa_phong', read_only=True)
    
    class Meta:
        model = PhongBenh
        fields = [
            'PhongID', 'ma_phong', 'ten_phong', 'loai_phong',
            'department_name', 'so_giuong', 'so_giuong_trong', 'trang_thai'
        ]


# Appointment Serializers
class AppointmentSerializer(BaseModelSerializer):
    """Appointment serializer."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    doctor_name = serializers.CharField(source='bac_si.ho_ten', read_only=True)
    department_name = serializers.CharField(source='bac_si.khoa_phong.ten_khoa_phong', read_only=True)
    
    class Meta:
        model = LichHen
        fields = [
            'LichHenID', 'ma_lich_hen', 'benh_nhan', 'patient_name',
            'bac_si', 'doctor_name', 'department_name', 'ngay_gio_hen',
            'loai_kham', 'ly_do_kham', 'trang_thai', 'ghi_chu_benh_nhan',
            'ghi_chu_bac_si', 'ket_qua_kham', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_lich_hen': {'read_only': True}
        }


class AppointmentCreateSerializer(BaseModelSerializer):
    """Appointment creation serializer."""
    
    class Meta:
        model = LichHen
        fields = [
            'benh_nhan', 'bac_si', 'ngay_gio_hen', 'loai_kham',
            'ly_do_kham', 'ghi_chu_benh_nhan'
        ]


class AppointmentListSerializer(serializers.ModelSerializer):
    """Simplified appointment serializer for lists."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    doctor_name = serializers.CharField(source='bac_si.ho_ten', read_only=True)
    
    class Meta:
        model = LichHen
        fields = [
            'LichHenID', 'ma_lich_hen', 'patient_name', 'doctor_name',
            'ngay_gio_hen', 'loai_kham', 'trang_thai'
        ]


# Medication Serializers
class MedicationSerializer(BaseModelSerializer):
    """Medication serializer."""
    
    is_low_stock = serializers.BooleanField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    stock_value = serializers.DecimalField(read_only=True, max_digits=15, decimal_places=0)
    
    class Meta:
        model = Thuoc
        fields = [
            'ThuocID', 'ma_thuoc', 'ten_thuoc', 'thanh_phan',
            'nong_do', 'dang_bao_che', 'dong_goi', 'don_vi_tinh',
            'hang_san_xuat', 'nuoc_san_xuat', 'so_dang_ky',
            'so_lo', 'ngay_san_xuat', 'ngay_het_han', 'is_expired',
            'gia_nhap', 'gia_ban', 'so_luong_ton_kho', 'is_low_stock',
            'muc_canh_bao_ton_kho', 'stock_value', 'vi_tri_kho',
            'trang_thai', 'ghi_chu', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_thuoc': {'read_only': True}
        }


class MedicationListSerializer(serializers.ModelSerializer):
    """Simplified medication serializer for lists."""
    
    is_low_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Thuoc
        fields = [
            'ThuocID', 'ma_thuoc', 'ten_thuoc', 'don_vi_tinh',
            'gia_ban', 'so_luong_ton_kho', 'is_low_stock', 'trang_thai'
        ]


# Prescription Serializers
class PrescriptionDetailSerializer(BaseModelSerializer):
    """Prescription detail serializer."""
    
    medication_name = serializers.CharField(source='thuoc.ten_thuoc', read_only=True)
    medication_unit = serializers.CharField(source='thuoc.get_don_vi_tinh_display', read_only=True)
    
    class Meta:
        model = ChiTietDonThuoc
        fields = [
            'ChiTietID', 'thuoc', 'medication_name', 'medication_unit',
            'so_luong', 'lieu_dung', 'tan_suat', 'thoi_gian_dung',
            'huong_dan_su_dung', 'don_gia', 'thanh_tien', 'da_cap', 'ghi_chu'
        ]


class PrescriptionSerializer(BaseModelSerializer):
    """Prescription serializer."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    doctor_name = serializers.CharField(source='bac_si.ho_ten', read_only=True)
    pharmacist_name = serializers.CharField(source='duoc_si.ho_ten', read_only=True)
    chi_tiet_don_thuoc = PrescriptionDetailSerializer(many=True, read_only=True)
    medication_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = DonThuoc
        fields = [
            'DonThuocID', 'ma_don_thuoc', 'benh_nhan', 'patient_name',
            'bac_si', 'doctor_name', 'duoc_si', 'pharmacist_name',
            'loai_don_thuoc', 'chan_doan', 'loi_dan_bac_si',
            'trang_thai', 'ghi_chu', 'tong_tien', 'medication_count',
            'ngay_ke_don', 'ngay_cap_thuoc', 'ngay_het_han',
            'chi_tiet_don_thuoc', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_don_thuoc': {'read_only': True}
        }


class PrescriptionCreateSerializer(BaseModelSerializer):
    """Prescription creation serializer."""
    
    class Meta:
        model = DonThuoc
        fields = [
            'benh_nhan', 'bac_si', 'loai_don_thuoc', 'chan_doan',
            'loi_dan_bac_si', 'ghi_chu'
        ]


class PrescriptionListSerializer(serializers.ModelSerializer):
    """Simplified prescription serializer for lists."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    doctor_name = serializers.CharField(source='bac_si.ho_ten', read_only=True)
    
    class Meta:
        model = DonThuoc
        fields = [
            'DonThuocID', 'ma_don_thuoc', 'patient_name', 'doctor_name',
            'loai_don_thuoc', 'trang_thai', 'ngay_ke_don', 'tong_tien'
        ]


# Bill Serializers
class BillSerializer(BaseModelSerializer):
    """Bill serializer."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    appointment_code = serializers.CharField(source='lich_hen.ma_lich_hen', read_only=True)
    prescription_code = serializers.CharField(source='don_thuoc.ma_don_thuoc', read_only=True)
    payment_status = serializers.CharField(source='payment_status_display', read_only=True)
    
    class Meta:
        model = HoaDon
        fields = [
            'HoaDonID', 'ma_hoa_don', 'benh_nhan', 'patient_name',
            'lich_hen', 'appointment_code', 'don_thuoc', 'prescription_code',
            'mo_ta', 'phi_kham', 'phi_thuoc', 'phi_dich_vu', 'tong_tien',
            'da_thanh_toan', 'con_no', 'bao_hiem_chi_tra',
            'phuong_thuc_thanh_toan', 'trang_thai', 'payment_status',
            'ghi_chu', 'ngay_tao', 'ngay_dao_han', 'ngay_thanh_toan',
            'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'ma_hoa_don': {'read_only': True}
        }


class BillCreateSerializer(BaseModelSerializer):
    """Bill creation serializer."""
    
    class Meta:
        model = HoaDon
        fields = [
            'benh_nhan', 'lich_hen', 'don_thuoc', 'mo_ta',
            'phi_kham', 'phi_thuoc', 'phi_dich_vu', 'ghi_chu'
        ]


class BillListSerializer(serializers.ModelSerializer):
    """Simplified bill serializer for lists."""
    
    patient_name = serializers.CharField(source='benh_nhan.ho_ten', read_only=True)
    
    class Meta:
        model = HoaDon
        fields = [
            'HoaDonID', 'ma_hoa_don', 'patient_name', 'tong_tien',
            'da_thanh_toan', 'con_no', 'trang_thai', 'ngay_tao'
        ]


# Payment Processing Serializers
class PaymentProcessSerializer(serializers.Serializer):
    """Payment processing serializer."""
    
    amount = serializers.DecimalField(max_digits=12, decimal_places=0, min_value=1)
    payment_method = serializers.ChoiceField(choices=HoaDon.LOAI_THANH_TOAN_CHOICES)
    notes = serializers.CharField(max_length=500, required=False)
    
    def validate_amount(self, value):
        bill = self.context.get('bill')
        if bill and value > bill.con_no:
            raise serializers.ValidationError(f"Số tiền thanh toán không được vượt quá số nợ ({bill.con_no:,.0f} VND)")
        return value


class InsuranceCoverageSerializer(serializers.Serializer):
    """Insurance coverage serializer."""
    
    coverage_amount = serializers.DecimalField(max_digits=12, decimal_places=0, min_value=0)
    notes = serializers.CharField(max_length=500, required=False)
    
    def validate_coverage_amount(self, value):
        bill = self.context.get('bill')
        if bill and value > bill.tong_tien:
            raise serializers.ValidationError(f"Số tiền bảo hiểm không được vượt quá tổng tiền ({bill.tong_tien:,.0f} VND)")
        return value


# Statistics Serializers
class DashboardStatsSerializer(serializers.Serializer):
    """Dashboard statistics serializer."""
    
    total_patients = serializers.IntegerField()
    total_appointments_today = serializers.IntegerField()
    total_revenue_today = serializers.DecimalField(max_digits=15, decimal_places=0)
    pending_bills = serializers.IntegerField()
    low_stock_medications = serializers.IntegerField()
    active_staff = serializers.IntegerField()
    available_rooms = serializers.IntegerField()


class RevenueStatsSerializer(serializers.Serializer):
    """Revenue statistics serializer."""
    
    date = serializers.DateField()
    total_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
    examination_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
    medication_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
    service_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
