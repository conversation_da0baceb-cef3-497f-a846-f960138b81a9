"""
Domain Interfaces and Abstract Classes
Defines contracts for Repository pattern and other domain abstractions.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from datetime import datetime

T = TypeVar('T')


class IRepository(ABC, Generic[T]):
    """
    Generic Repository Interface following Repository pattern.
    Provides contract for data access operations.
    """
    
    @abstractmethod
    async def get_by_id(self, entity_id: int) -> Optional[T]:
        """Get entity by ID."""
        pass
    
    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """Get all entities with pagination."""
        pass
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """Create new entity."""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """Update existing entity."""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: int) -> bool:
        """Delete entity by ID."""
        pass
    
    @abstractmethod
    async def exists(self, entity_id: int) -> bool:
        """Check if entity exists."""
        pass
    
    @abstractmethod
    async def count(self, filters: Dict[str, Any] = None) -> int:
        """Count entities with optional filters."""
        pass


class IUnitOfWork(ABC):
    """
    Unit of Work interface for transaction management.
    Ensures atomicity across multiple repository operations.
    """
    
    @abstractmethod
    async def begin(self) -> None:
        """Begin transaction."""
        pass
    
    @abstractmethod
    async def commit(self) -> None:
        """Commit transaction."""
        pass
    
    @abstractmethod
    async def rollback(self) -> None:
        """Rollback transaction."""
        pass
    
    @abstractmethod
    async def save_changes(self) -> None:
        """Save all changes in current transaction."""
        pass


class IDomainEventDispatcher(ABC):
    """
    Domain Event Dispatcher interface following Observer pattern.
    Handles publishing and subscribing to domain events.
    """
    
    @abstractmethod
    async def dispatch(self, event: Any) -> None:
        """Dispatch domain event to all subscribers."""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: type, handler: callable) -> None:
        """Subscribe handler to specific event type."""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: type, handler: callable) -> None:
        """Unsubscribe handler from event type."""
        pass


class INotificationService(ABC):
    """
    Notification Service interface following Strategy pattern.
    Supports different notification channels.
    """
    
    @abstractmethod
    async def send_email(self, to: str, subject: str, body: str, **kwargs) -> bool:
        """Send email notification."""
        pass
    
    @abstractmethod
    async def send_sms(self, phone: str, message: str, **kwargs) -> bool:
        """Send SMS notification."""
        pass
    
    @abstractmethod
    async def send_push(self, user_id: int, title: str, body: str, **kwargs) -> bool:
        """Send push notification."""
        pass


class IFileStorageService(ABC):
    """
    File Storage Service interface for file operations.
    Abstracts different storage implementations.
    """
    
    @abstractmethod
    async def upload(self, file_data: bytes, file_name: str, folder: str = None) -> str:
        """Upload file and return file path/URL."""
        pass
    
    @abstractmethod
    async def download(self, file_path: str) -> bytes:
        """Download file by path."""
        pass
    
    @abstractmethod
    async def delete(self, file_path: str) -> bool:
        """Delete file by path."""
        pass
    
    @abstractmethod
    async def exists(self, file_path: str) -> bool:
        """Check if file exists."""
        pass


class ICacheService(ABC):
    """
    Cache Service interface for caching operations.
    Supports different cache implementations.
    """
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Set value in cache with optional TTL."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        pass
    
    @abstractmethod
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        pass


class IReportGenerator(ABC):
    """
    Report Generator interface following Strategy pattern.
    Supports different report formats.
    """
    
    @abstractmethod
    async def generate_pdf(self, data: Dict[str, Any], template: str) -> bytes:
        """Generate PDF report."""
        pass
    
    @abstractmethod
    async def generate_excel(self, data: Dict[str, Any], template: str) -> bytes:
        """Generate Excel report."""
        pass
    
    @abstractmethod
    async def generate_csv(self, data: List[Dict[str, Any]]) -> bytes:
        """Generate CSV report."""
        pass


class IPaymentProcessor(ABC):
    """
    Payment Processor interface following Strategy pattern.
    Supports different payment methods.
    """
    
    @abstractmethod
    async def process_cash_payment(self, amount: float, **kwargs) -> Dict[str, Any]:
        """Process cash payment."""
        pass
    
    @abstractmethod
    async def process_card_payment(self, amount: float, card_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Process card payment."""
        pass
    
    @abstractmethod
    async def process_insurance_payment(self, amount: float, insurance_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Process insurance payment."""
        pass
    
    @abstractmethod
    async def refund_payment(self, payment_id: str, amount: float = None) -> Dict[str, Any]:
        """Process payment refund."""
        pass


class IValidator(ABC):
    """
    Generic Validator interface for input validation.
    """
    
    @abstractmethod
    def validate(self, data: Any) -> List[str]:
        """Validate data and return list of error messages."""
        pass
    
    @abstractmethod
    def is_valid(self, data: Any) -> bool:
        """Check if data is valid."""
        pass


class ISpecification(ABC, Generic[T]):
    """
    Specification interface for business rules validation.
    Implements Specification pattern for complex business logic.
    """
    
    @abstractmethod
    def is_satisfied_by(self, entity: T) -> bool:
        """Check if entity satisfies specification."""
        pass
    
    def and_specification(self, other: 'ISpecification[T]') -> 'ISpecification[T]':
        """Combine specifications with AND logic."""
        return AndSpecification(self, other)
    
    def or_specification(self, other: 'ISpecification[T]') -> 'ISpecification[T]':
        """Combine specifications with OR logic."""
        return OrSpecification(self, other)
    
    def not_specification(self) -> 'ISpecification[T]':
        """Negate specification."""
        return NotSpecification(self)


class AndSpecification(ISpecification[T]):
    """AND combination of two specifications."""
    
    def __init__(self, left: ISpecification[T], right: ISpecification[T]):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, entity: T) -> bool:
        return self.left.is_satisfied_by(entity) and self.right.is_satisfied_by(entity)


class OrSpecification(ISpecification[T]):
    """OR combination of two specifications."""
    
    def __init__(self, left: ISpecification[T], right: ISpecification[T]):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, entity: T) -> bool:
        return self.left.is_satisfied_by(entity) or self.right.is_satisfied_by(entity)


class NotSpecification(ISpecification[T]):
    """NOT negation of a specification."""
    
    def __init__(self, specification: ISpecification[T]):
        self.specification = specification
    
    def is_satisfied_by(self, entity: T) -> bool:
        return not self.specification.is_satisfied_by(entity)
