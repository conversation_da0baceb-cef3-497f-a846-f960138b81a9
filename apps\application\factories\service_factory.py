"""
Service Factory Implementation
Implements Factory pattern for creating application services with proper dependency injection.
"""
from typing import Dict, Any, Type
from apps.application.interfaces.services import (
    IUserService, IPatientService, IAppointmentService, IMedicalStaffService,
    IMedicationService, IPrescriptionService, IBillingService, IRoomService, IReportService
)


class ServiceFactory:
    """
    Factory class for creating application services.
    Implements Factory pattern and Dependency Injection pattern.
    """
    
    def __init__(self):
        self._services: Dict[str, Type] = {}
        self._instances: Dict[str, Any] = {}
    
    def register_service(self, interface: Type, implementation: Type) -> None:
        """Register service implementation for interface."""
        self._services[interface.__name__] = implementation
    
    def create_service(self, service_interface: Type, **kwargs) -> Any:
        """
        Create service instance using Factory pattern.
        Returns singleton instances for better performance.
        """
        service_name = service_interface.__name__
        
        # Return existing instance if available (Singleton pattern)
        if service_name in self._instances:
            return self._instances[service_name]
        
        # Create new instance
        if service_name not in self._services:
            raise ValueError(f"Service {service_name} not registered")
        
        service_class = self._services[service_name]
        service_instance = service_class(**kwargs)
        
        # Cache instance
        self._instances[service_name] = service_instance
        
        return service_instance
    
    def get_user_service(self, **kwargs) -> IUserService:
        """Get User service instance."""
        return self.create_service(IUserService, **kwargs)
    
    def get_patient_service(self, **kwargs) -> IPatientService:
        """Get Patient service instance."""
        return self.create_service(IPatientService, **kwargs)
    
    def get_appointment_service(self, **kwargs) -> IAppointmentService:
        """Get Appointment service instance."""
        return self.create_service(IAppointmentService, **kwargs)
    
    def get_medical_staff_service(self, **kwargs) -> IMedicalStaffService:
        """Get Medical Staff service instance."""
        return self.create_service(IMedicalStaffService, **kwargs)
    
    def get_medication_service(self, **kwargs) -> IMedicationService:
        """Get Medication service instance."""
        return self.create_service(IMedicationService, **kwargs)
    
    def get_prescription_service(self, **kwargs) -> IPrescriptionService:
        """Get Prescription service instance."""
        return self.create_service(IPrescriptionService, **kwargs)
    
    def get_billing_service(self, **kwargs) -> IBillingService:
        """Get Billing service instance."""
        return self.create_service(IBillingService, **kwargs)
    
    def get_room_service(self, **kwargs) -> IRoomService:
        """Get Room service instance."""
        return self.create_service(IRoomService, **kwargs)
    
    def get_report_service(self, **kwargs) -> IReportService:
        """Get Report service instance."""
        return self.create_service(IReportService, **kwargs)
    
    def clear_cache(self) -> None:
        """Clear service instance cache."""
        self._instances.clear()


# Global service factory instance
service_factory = ServiceFactory()


class ResponseFactory:
    """
    Factory for creating standardized API responses.
    Ensures consistent response format across all endpoints.
    """
    
    @staticmethod
    def success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create success response."""
        return {
            "success": True,
            "message": message,
            "data": data,
            "error": None
        }
    
    @staticmethod
    def error_response(message: str, error_code: str = None, details: Any = None) -> Dict[str, Any]:
        """Create error response."""
        return {
            "success": False,
            "message": message,
            "data": None,
            "error": {
                "code": error_code,
                "details": details
            }
        }
    
    @staticmethod
    def validation_error_response(errors: Dict[str, Any]) -> Dict[str, Any]:
        """Create validation error response."""
        return {
            "success": False,
            "message": "Validation failed",
            "data": None,
            "error": {
                "code": "VALIDATION_ERROR",
                "details": errors
            }
        }
    
    @staticmethod
    def paginated_response(
        data: list, 
        total: int, 
        page: int, 
        page_size: int, 
        message: str = "Success"
    ) -> Dict[str, Any]:
        """Create paginated response."""
        return {
            "success": True,
            "message": message,
            "data": data,
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            },
            "error": None
        }
