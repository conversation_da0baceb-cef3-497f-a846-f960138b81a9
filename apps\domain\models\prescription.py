"""
Prescription Domain Models
Defines the Prescription and Prescription Detail entities for medication prescriptions.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from apps.domain.models.patient import <PERSON><PERSON><PERSON><PERSON>
from apps.domain.models.medical_staff import NhanVienYTe
from apps.domain.models.medication import <PERSON>huoc
from decimal import Decimal


class DonThuoc(BaseEntity):
    """
    Prescription model for hospital management system.
    Represents medication prescriptions issued by doctors.
    """
    
    TRANG_THAI_CHOICES = [
        ('Draft', 'Bản nháp'),
        ('Issued', 'Đã kê đơn'),
        ('Dispensed', 'Đã cấp thuốc'),
        ('Partially_Dispensed', '<PERSON><PERSON><PERSON> thuốc một phần'),
        ('Completed', 'Hoàn thành'),
        ('Cancelled', 'Đã hủy'),
        ('Expired', 'Hết hạn'),
    ]
    
    LOAI_DON_THUOC_CHOICES = [
        ('Noi_Tru', 'Nội trú'),
        ('<PERSON>oa<PERSON>_Tru', 'Ngoại trú'),
        ('Cap_Cuu', '<PERSON><PERSON><PERSON> cứu'),
        ('<PERSON>ham_Suc_Khoe', 'Khám sức khỏe'),
    ]
    
    # Primary Key
    DonThuocID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    benh_nhan = models.ForeignKey(
        BenhNhan,
        on_delete=models.CASCADE,
        related_name='don_thuoc',
        verbose_name='Bệnh nhân',
        help_text='Bệnh nhân được kê đơn thuốc'
    )
    
    bac_si = models.ForeignKey(
        NhanVienYTe,
        on_delete=models.PROTECT,
        related_name='don_thuoc_ke_don',
        verbose_name='Bác sĩ kê đơn',
        help_text='Bác sĩ kê đơn thuốc',
        limit_choices_to={'loai_nhan_vien': 'Bac_Si'}
    )
    
    duoc_si = models.ForeignKey(
        NhanVienYTe,
        on_delete=models.PROTECT,
        related_name='don_thuoc_cap_thuoc',
        verbose_name='Dược sĩ',
        blank=True,
        null=True,
        help_text='Dược sĩ cấp thuốc',
        limit_choices_to={'loai_nhan_vien': 'Duoc_Si'}
    )
    
    # Prescription Information
    ma_don_thuoc = models.CharField(
        'Mã đơn thuốc',
        max_length=30,
        unique=True,
        help_text='Mã định danh duy nhất của đơn thuốc'
    )
    
    loai_don_thuoc = models.CharField(
        'Loại đơn thuốc',
        max_length=20,
        choices=LOAI_DON_THUOC_CHOICES,
        default='Ngoai_Tru',
        help_text='Loại đơn thuốc'
    )
    
    chan_doan = models.TextField(
        'Chẩn đoán',
        help_text='Chẩn đoán bệnh'
    )
    
    loi_dan_bac_si = models.TextField(
        'Lời dặn bác sĩ',
        blank=True,
        null=True,
        help_text='Lời dặn của bác sĩ cho bệnh nhân'
    )
    
    # Status and Tracking
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Draft',
        help_text='Trạng thái đơn thuốc'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú thêm về đơn thuốc'
    )
    
    # Financial Information
    tong_tien = models.DecimalField(
        'Tổng tiền',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Tổng tiền của đơn thuốc (VND)'
    )
    
    # Timestamps
    ngay_ke_don = models.DateTimeField(
        'Ngày kê đơn',
        default=timezone.now,
        help_text='Ngày kê đơn thuốc'
    )
    
    ngay_cap_thuoc = models.DateTimeField(
        'Ngày cấp thuốc',
        blank=True,
        null=True,
        help_text='Ngày cấp thuốc'
    )
    
    ngay_het_han = models.DateField(
        'Ngày hết hạn',
        blank=True,
        null=True,
        help_text='Ngày hết hạn của đơn thuốc'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật lần cuối'
    )
    
    class Meta:
        db_table = 'DonThuoc'
        verbose_name = 'Đơn thuốc'
        verbose_name_plural = 'Đơn thuốc'
        ordering = ['-ngay_ke_don']
        indexes = [
            models.Index(fields=['ma_don_thuoc']),
            models.Index(fields=['benh_nhan', 'trang_thai']),
            models.Index(fields=['bac_si', 'ngay_ke_don']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['loai_don_thuoc']),
            models.Index(fields=['ngay_ke_don']),
            models.Index(fields=['ngay_het_han']),
        ]
    
    def __str__(self):
        return f"Đơn thuốc {self.ma_don_thuoc} - {self.benh_nhan.ho_ten}"
    
    def save(self, *args, **kwargs):
        """Override save to generate prescription code and calculate total."""
        # Auto-generate prescription code if not provided
        if not self.ma_don_thuoc:
            self.ma_don_thuoc = self.generate_prescription_code()
        
        # Set expiry date if not set (30 days from issue date)
        if not self.ngay_het_han:
            from datetime import timedelta
            self.ngay_het_han = (self.ngay_ke_don + timedelta(days=30)).date()
        
        self.full_clean()
        super().save(*args, **kwargs)
        
        # Recalculate total after save
        self.calculate_total()
    
    def clean(self):
        """Validate prescription fields."""
        # Validate expiry date
        if self.ngay_het_han and self.ngay_het_han <= self.ngay_ke_don.date():
            raise ValidationError({
                'ngay_het_han': 'Ngày hết hạn phải sau ngày kê đơn'
            })
        
        # Validate doctor can prescribe medication
        if self.bac_si and not self.bac_si.can_prescribe_medication():
            raise ValidationError({
                'bac_si': 'Bác sĩ được chọn không thể kê đơn thuốc'
            })
        
        # Validate pharmacist can dispense (if set)
        if self.duoc_si and not self.duoc_si.can_dispense_medication():
            raise ValidationError({
                'duoc_si': 'Dược sĩ được chọn không thể cấp thuốc'
            })
    
    def generate_prescription_code(self) -> str:
        """Generate unique prescription code."""
        from datetime import datetime
        
        # Format: RX + Type prefix + YYYYMMDD + sequential number
        type_prefixes = {
            'Noi_Tru': 'NT',
            'Ngoai_Tru': 'NGT',
            'Cap_Cuu': 'CC',
            'Kham_Suc_Khoe': 'KSK'
        }
        
        today = datetime.now()
        date_str = today.strftime('%Y%m%d')
        prefix = f"RX{type_prefixes.get(self.loai_don_thuoc, 'NGT')}{date_str}"
        
        # Find highest sequential number for today
        existing_codes = DonThuoc.objects.filter(
            ma_don_thuoc__startswith=prefix
        ).values_list('ma_don_thuoc', flat=True)
        
        max_seq = 0
        for code in existing_codes:
            try:
                seq = int(code[len(prefix):])
                max_seq = max(max_seq, seq)
            except (ValueError, IndexError):
                continue
        
        return f"{prefix}{max_seq + 1:04d}"
    
    def calculate_total(self):
        """Calculate total amount of prescription."""
        total = sum(
            detail.thanh_tien for detail in self.chi_tiet_don_thuoc.all()
        )
        
        if self.tong_tien != total:
            self.tong_tien = total
            self.save(update_fields=['tong_tien', 'ngay_cap_nhat'])
    
    # Business Methods
    def is_draft(self) -> bool:
        """Check if prescription is in draft state."""
        return self.trang_thai == 'Draft'
    
    def is_issued(self) -> bool:
        """Check if prescription is issued."""
        return self.trang_thai == 'Issued'
    
    def is_dispensed(self) -> bool:
        """Check if prescription is fully dispensed."""
        return self.trang_thai == 'Dispensed'
    
    def is_completed(self) -> bool:
        """Check if prescription is completed."""
        return self.trang_thai == 'Completed'
    
    def is_cancelled(self) -> bool:
        """Check if prescription is cancelled."""
        return self.trang_thai == 'Cancelled'
    
    def is_expired(self) -> bool:
        """Check if prescription is expired."""
        if not self.ngay_het_han:
            return False
        return timezone.now().date() > self.ngay_het_han
    
    def can_be_dispensed(self) -> bool:
        """Check if prescription can be dispensed."""
        return (self.is_issued() and 
                not self.is_expired() and 
                self.chi_tiet_don_thuoc.exists())
    
    def get_medication_count(self) -> int:
        """Get number of different medications in prescription."""
        return self.chi_tiet_don_thuoc.count()
    
    def get_total_items(self) -> int:
        """Get total number of medication items."""
        return sum(
            detail.so_luong for detail in self.chi_tiet_don_thuoc.all()
        )
    
    def issue_prescription(self):
        """Issue the prescription."""
        if not self.is_draft():
            raise ValueError("Chỉ có thể phát hành đơn thuốc ở trạng thái bản nháp")
        
        if not self.chi_tiet_don_thuoc.exists():
            raise ValueError("Đơn thuốc phải có ít nhất một loại thuốc")
        
        self.trang_thai = 'Issued'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_prescription_issued_event()
    
    def dispense_prescription(self, pharmacist: NhanVienYTe, notes: str = None):
        """Dispense the prescription."""
        if not self.can_be_dispensed():
            raise ValueError("Đơn thuốc không thể cấp thuốc")
        
        if not pharmacist.can_dispense_medication():
            raise ValueError("Dược sĩ không có quyền cấp thuốc")
        
        # Check if all medications can be dispensed
        insufficient_stock = []
        for detail in self.chi_tiet_don_thuoc.all():
            if not detail.thuoc.can_dispense(detail.so_luong):
                insufficient_stock.append(detail.thuoc.ten_thuoc)
        
        if insufficient_stock:
            raise ValueError(f"Không đủ thuốc: {', '.join(insufficient_stock)}")
        
        # Reduce stock for all medications
        for detail in self.chi_tiet_don_thuoc.all():
            detail.thuoc.reduce_stock(detail.so_luong, f"Cấp theo đơn {self.ma_don_thuoc}")
        
        self.duoc_si = pharmacist
        self.trang_thai = 'Dispensed'
        self.ngay_cap_thuoc = timezone.now()
        
        if notes:
            self.ghi_chu = f"{self.ghi_chu or ''}\n{notes}".strip()
        
        self.save(update_fields=['duoc_si', 'trang_thai', 'ngay_cap_thuoc', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_prescription_dispensed_event()
    
    def cancel_prescription(self, reason: str, cancelled_by: NhanVienYTe = None):
        """Cancel the prescription."""
        if self.is_completed() or self.is_cancelled():
            raise ValueError("Không thể hủy đơn thuốc đã hoàn thành hoặc đã hủy")
        
        # If already dispensed, return stock
        if self.is_dispensed():
            for detail in self.chi_tiet_don_thuoc.all():
                detail.thuoc.add_stock(detail.so_luong, f"Hoàn trả từ đơn hủy {self.ma_don_thuoc}")
        
        old_status = self.trang_thai
        self.trang_thai = 'Cancelled'
        
        cancel_note = f"Hủy đơn: {reason}"
        if cancelled_by:
            cancel_note += f" (bởi {cancelled_by.ho_ten})"
        
        self.ghi_chu = f"{self.ghi_chu or ''}\n{cancel_note}".strip()
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_prescription_cancelled_event(reason, old_status)
    
    def complete_prescription(self):
        """Mark prescription as completed."""
        if not self.is_dispensed():
            raise ValueError("Đơn thuốc phải được cấp thuốc trước khi hoàn thành")
        
        self.trang_thai = 'Completed'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_prescription_completed_event()
    
    def add_medication(self, medication: Thuoc, quantity: int, dosage: str, 
                      frequency: str, duration: str, instructions: str = None):
        """Add medication to prescription."""
        if not self.is_draft():
            raise ValueError("Chỉ có thể thêm thuốc vào đơn thuốc ở trạng thái bản nháp")
        
        detail = ChiTietDonThuoc.objects.create(
            don_thuoc=self,
            thuoc=medication,
            so_luong=quantity,
            lieu_dung=dosage,
            tan_suat=frequency,
            thoi_gian_dung=duration,
            huong_dan_su_dung=instructions or ''
        )
        
        # Recalculate total
        self.calculate_total()
        
        return detail
    
    @property
    def prescription_summary(self) -> dict:
        """Get prescription summary."""
        return {
            'code': self.ma_don_thuoc,
            'patient': self.benh_nhan.ho_ten,
            'doctor': self.bac_si.ho_ten,
            'pharmacist': self.duoc_si.ho_ten if self.duoc_si else None,
            'type': self.get_loai_don_thuoc_display(),
            'status': self.get_trang_thai_display(),
            'diagnosis': self.chan_doan,
            'medication_count': self.get_medication_count(),
            'total_amount': float(self.tong_tien),
            'issue_date': self.ngay_ke_don,
            'expiry_date': self.ngay_het_han,
            'is_expired': self.is_expired()
        }
    
    # Domain Events
    def raise_prescription_created_event(self):
        """Raise prescription created domain event."""
        from apps.domain.events.domain_events import PrescriptionCreatedEvent
        event = PrescriptionCreatedEvent(
            prescription_id=self.DonThuocID,
            prescription_code=self.ma_don_thuoc,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            prescription_type=self.loai_don_thuoc,
            diagnosis=self.chan_doan,
            created_at=self.ngay_ke_don
        )
        self.add_domain_event(event)
    
    def raise_prescription_issued_event(self):
        """Raise prescription issued domain event."""
        from apps.domain.events.domain_events import PrescriptionIssuedEvent
        event = PrescriptionIssuedEvent(
            prescription_id=self.DonThuocID,
            prescription_code=self.ma_don_thuoc,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            medication_count=self.get_medication_count(),
            total_amount=float(self.tong_tien),
            issued_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_prescription_dispensed_event(self):
        """Raise prescription dispensed domain event."""
        from apps.domain.events.domain_events import PrescriptionDispensedEvent
        event = PrescriptionDispensedEvent(
            prescription_id=self.DonThuocID,
            prescription_code=self.ma_don_thuoc,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            pharmacist_id=self.duoc_si.NhanVienID,
            total_amount=float(self.tong_tien),
            dispensed_at=self.ngay_cap_thuoc
        )
        self.add_domain_event(event)
    
    def raise_prescription_cancelled_event(self, reason: str, old_status: str):
        """Raise prescription cancelled domain event."""
        from apps.domain.events.domain_events import PrescriptionCancelledEvent
        event = PrescriptionCancelledEvent(
            prescription_id=self.DonThuocID,
            prescription_code=self.ma_don_thuoc,
            patient_id=self.benh_nhan.BenhNhanID,
            doctor_id=self.bac_si.NhanVienID,
            cancellation_reason=reason,
            old_status=old_status,
            cancelled_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_prescription_completed_event(self):
        """Raise prescription completed domain event."""
        from apps.domain.events.domain_events import PrescriptionCompletedEvent
        event = PrescriptionCompletedEvent(
            prescription_id=self.DonThuocID,
            prescription_code=self.ma_don_thuoc,
            patient_id=self.benh_nhan.BenhNhanID,
            completed_at=timezone.now()
        )
        self.add_domain_event(event)


class ChiTietDonThuoc(BaseEntity):
    """
    Prescription Detail model for individual medication items in a prescription.
    """
    
    # Primary Key
    ChiTietID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    don_thuoc = models.ForeignKey(
        DonThuoc,
        on_delete=models.CASCADE,
        related_name='chi_tiet_don_thuoc',
        verbose_name='Đơn thuốc',
        help_text='Đơn thuốc chứa chi tiết này'
    )
    
    thuoc = models.ForeignKey(
        Thuoc,
        on_delete=models.PROTECT,
        related_name='chi_tiet_don_thuoc',
        verbose_name='Thuốc',
        help_text='Thuốc được kê trong chi tiết này'
    )
    
    # Medication Details
    so_luong = models.IntegerField(
        'Số lượng',
        help_text='Số lượng thuốc được kê'
    )
    
    lieu_dung = models.CharField(
        'Liều dùng',
        max_length=100,
        help_text='Liều dùng của thuốc (VD: 1 viên, 5ml)'
    )
    
    tan_suat = models.CharField(
        'Tần suất',
        max_length=100,
        help_text='Tần suất sử dụng (VD: 3 lần/ngày, mỗi 8 giờ)'
    )
    
    thoi_gian_dung = models.CharField(
        'Thời gian dùng',
        max_length=100,
        help_text='Thời gian sử dụng thuốc (VD: 7 ngày, 2 tuần)'
    )
    
    huong_dan_su_dung = models.TextField(
        'Hướng dẫn sử dụng',
        blank=True,
        null=True,
        help_text='Hướng dẫn chi tiết cách sử dụng thuốc'
    )
    
    # Pricing
    don_gia = models.DecimalField(
        'Đơn giá',
        max_digits=12,
        decimal_places=0,
        help_text='Đơn giá của thuốc (VND)'
    )
    
    thanh_tien = models.DecimalField(
        'Thành tiền',
        max_digits=12,
        decimal_places=0,
        help_text='Thành tiền (Số lượng × Đơn giá)'
    )
    
    # Status
    da_cap = models.BooleanField(
        'Đã cấp',
        default=False,
        help_text='Thuốc đã được cấp hay chưa'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú về chi tiết thuốc'
    )
    
    class Meta:
        db_table = 'ChiTietDonThuoc'
        verbose_name = 'Chi tiết đơn thuốc'
        verbose_name_plural = 'Chi tiết đơn thuốc'
        ordering = ['don_thuoc', 'thuoc']
        indexes = [
            models.Index(fields=['don_thuoc']),
            models.Index(fields=['thuoc']),
            models.Index(fields=['da_cap']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(so_luong__gt=0),
                name='check_positive_quantity'
            ),
            models.CheckConstraint(
                check=models.Q(don_gia__gt=0),
                name='check_positive_unit_price'
            ),
            models.CheckConstraint(
                check=models.Q(thanh_tien__gte=0),
                name='check_non_negative_total'
            ),
        ]
        unique_together = ['don_thuoc', 'thuoc']
    
    def __str__(self):
        return f"{self.thuoc.ten_thuoc} - {self.so_luong} {self.thuoc.get_don_vi_tinh_display()}"
    
    def save(self, *args, **kwargs):
        """Override save to calculate amount and validate."""
        # Get current medication price if not set
        if not self.don_gia:
            self.don_gia = self.thuoc.gia_ban
        
        # Calculate total amount
        self.thanh_tien = self.don_gia * self.so_luong
        
        self.full_clean()
        super().save(*args, **kwargs)
        
        # Update prescription total
        if self.don_thuoc_id:
            self.don_thuoc.calculate_total()
    
    def clean(self):
        """Validate prescription detail fields."""
        # Validate medication availability
        if self.thuoc and not self.thuoc.is_available():
            raise ValidationError({
                'thuoc': f'Thuốc {self.thuoc.ten_thuoc} không có sẵn'
            })
        
        # Validate quantity against stock
        if self.thuoc and self.so_luong > self.thuoc.so_luong_ton_kho:
            raise ValidationError({
                'so_luong': f'Không đủ thuốc trong kho. Chỉ còn {self.thuoc.so_luong_ton_kho} {self.thuoc.get_don_vi_tinh_display()}'
            })
        
        # Validate dosage format
        if not self.lieu_dung.strip():
            raise ValidationError({
                'lieu_dung': 'Liều dùng không được để trống'
            })
    
    # Business Methods
    def is_dispensed(self) -> bool:
        """Check if medication is dispensed."""
        return self.da_cap
    
    def get_total_daily_amount(self) -> str:
        """Get total daily medication amount (for dosage calculation)."""
        # This would require parsing frequency and dosage
        # For now, return a simple description
        return f"{self.lieu_dung} × {self.tan_suat}"
    
    def dispense_medication(self):
        """Mark medication as dispensed."""
        if self.da_cap:
            raise ValueError("Thuốc đã được cấp")
        
        if not self.thuoc.can_dispense(self.so_luong):
            raise ValueError("Không thể cấp thuốc do không đủ tồn kho")
        
        self.da_cap = True
        self.save(update_fields=['da_cap'])
    
    def update_quantity(self, new_quantity: int):
        """Update medication quantity."""
        if self.don_thuoc.is_dispensed():
            raise ValueError("Không thể thay đổi số lượng sau khi đã cấp thuốc")
        
        old_quantity = self.so_luong
        self.so_luong = new_quantity
        self.thanh_tien = self.don_gia * new_quantity
        
        self.save(update_fields=['so_luong', 'thanh_tien'])
        
        # Update prescription total
        self.don_thuoc.calculate_total()
    
    def update_instructions(self, dosage: str, frequency: str, duration: str, instructions: str = None):
        """Update medication instructions."""
        if self.don_thuoc.is_dispensed():
            raise ValueError("Không thể thay đổi hướng dẫn sau khi đã cấp thuốc")
        
        self.lieu_dung = dosage
        self.tan_suat = frequency
        self.thoi_gian_dung = duration
        
        if instructions is not None:
            self.huong_dan_su_dung = instructions
        
        self.save(update_fields=[
            'lieu_dung', 'tan_suat', 'thoi_gian_dung', 'huong_dan_su_dung'
        ])
    
    @property
    def medication_summary(self) -> dict:
        """Get medication detail summary."""
        return {
            'medication_name': self.thuoc.ten_thuoc,
            'medication_code': self.thuoc.ma_thuoc,
            'quantity': self.so_luong,
            'unit': self.thuoc.get_don_vi_tinh_display(),
            'dosage': self.lieu_dung,
            'frequency': self.tan_suat,
            'duration': self.thoi_gian_dung,
            'instructions': self.huong_dan_su_dung,
            'unit_price': float(self.don_gia),
            'total_price': float(self.thanh_tien),
            'is_dispensed': self.da_cap
        }
