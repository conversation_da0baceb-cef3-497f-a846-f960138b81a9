"""
Custom permissions for Hospital Management System
Implements role-based access control and business-specific permissions.
"""
from rest_framework import permissions
from apps.domain.models.user import NguoiDung


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only to the owner
        return obj.owner == request.user


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow admins to edit, others can read.
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        
        return request.user.is_authenticated and request.user.vai_tro == 'Quan_Tri_Vien'


class IsDoctorOrNurse(permissions.BasePermission):
    """
    Permission for medical staff (doctors and nurses).
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro in ['Bac_Si', 'Y_Ta'])


class IsDoctor(permissions.BasePermission):
    """
    Permission for doctors only.
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Bac_Si')


class IsPharmacist(permissions.BasePermission):
    """
    Permission for pharmacists only.
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Duoc_Si')


class IsReceptionist(permissions.BasePermission):
    """
    Permission for receptionists.
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Le_Tan')


class IsAccountant(permissions.BasePermission):
    """
    Permission for accountants.
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Ke_Toan')


class CanManagePatients(permissions.BasePermission):
    """
    Permission to manage patient records.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Bac_Si', 'Y_Ta', 'Le_Tan']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanManageAppointments(permissions.BasePermission):
    """
    Permission to manage appointments.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Bac_Si', 'Y_Ta', 'Le_Tan']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)
    
    def has_object_permission(self, request, view, obj):
        # Doctors can only manage their own appointments
        if request.user.vai_tro == 'Bac_Si':
            return obj.bac_si.nguoi_dung == request.user
        
        # Others with permission can manage all
        return self.has_permission(request, view)


class CanPrescribeMedication(permissions.BasePermission):
    """
    Permission to prescribe medications (doctors only).
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Bac_Si')
    
    def has_object_permission(self, request, view, obj):
        # Doctors can only manage prescriptions they created
        if hasattr(obj, 'bac_si'):
            return obj.bac_si.nguoi_dung == request.user
        return True


class CanDispenseMedication(permissions.BasePermission):
    """
    Permission to dispense medications (pharmacists only).
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Duoc_Si')


class CanManageBills(permissions.BasePermission):
    """
    Permission to manage billing.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Ke_Toan', 'Le_Tan']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanProcessPayments(permissions.BasePermission):
    """
    Permission to process payments.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Ke_Toan', 'Le_Tan']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanViewReports(permissions.BasePermission):
    """
    Permission to view reports and statistics.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Ke_Toan', 'Truong_Khoa']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanManageStaff(permissions.BasePermission):
    """
    Permission to manage medical staff.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Truong_Khoa']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanManageDepartments(permissions.BasePermission):
    """
    Permission to manage departments.
    """
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and 
                request.user.vai_tro == 'Quan_Tri_Vien')


class CanManageRooms(permissions.BasePermission):
    """
    Permission to manage rooms.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Y_Ta', 'Le_Tan']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


class CanManageMedications(permissions.BasePermission):
    """
    Permission to manage medication inventory.
    """
    
    def has_permission(self, request, view):
        allowed_roles = ['Quan_Tri_Vien', 'Duoc_Si']
        return (request.user.is_authenticated and 
                request.user.vai_tro in allowed_roles)


# Role-based permission mixins
class AdminRequiredMixin:
    """Mixin for admin-only views."""
    permission_classes = [permissions.IsAuthenticated, IsAdminOrReadOnly]


class DoctorRequiredMixin:
    """Mixin for doctor-only views."""
    permission_classes = [permissions.IsAuthenticated, IsDoctor]


class PharmacistRequiredMixin:
    """Mixin for pharmacist-only views."""
    permission_classes = [permissions.IsAuthenticated, IsPharmacist]


class ReceptionistRequiredMixin:
    """Mixin for receptionist views."""
    permission_classes = [permissions.IsAuthenticated, IsReceptionist]


class AccountantRequiredMixin:
    """Mixin for accountant views."""
    permission_classes = [permissions.IsAuthenticated, IsAccountant]
