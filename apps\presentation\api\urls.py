"""
URL routing for Hospital Management API
Defines REST API endpoints and URL patterns.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework.authtoken.views import obtain_auth_token

from .views import (
    CustomAuthToken,
    UserViewSet,
    DepartmentViewSet,
    PatientViewSet,
    MedicalStaffViewSet,
    RoomViewSet,
    AppointmentViewSet,
    MedicationViewSet,
    PrescriptionViewSet,
    BillViewSet,
    DashboardViewSet
)

# Create router for ViewSets
router = DefaultRouter()

# Register ViewSets with router
router.register(r'users', UserViewSet)
router.register(r'departments', DepartmentViewSet)
router.register(r'patients', PatientViewSet)
router.register(r'medical-staff', MedicalStaffViewSet)
router.register(r'rooms', RoomViewSet)
router.register(r'appointments', AppointmentViewSet)
router.register(r'medications', MedicationViewSet)
router.register(r'prescriptions', PrescriptionViewSet)
router.register(r'bills', BillViewSet)
router.register(r'dashboard', DashboardViewSet, basename='dashboard')

# URL patterns
urlpatterns = [
    # Authentication endpoints
    path('auth/login/', CustomAuthToken.as_view(), name='api_token_auth'),
    path('auth/token/', obtain_auth_token, name='api_token'),
    
    # API root and ViewSet URLs
    path('', include(router.urls)),
    
    # API documentation (optional - can be added later)
    # path('docs/', include_docs_urls(title='Hospital Management API')),
]
