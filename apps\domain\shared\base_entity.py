"""
Base Entity for Domain Models
Implements common functionality for all domain entities following DDD principles.
"""
from abc import ABC
from datetime import datetime
from typing import Any, Dict, List
from uuid import uuid4


class DomainEvent:
    """Base class for domain events following Observer pattern."""
    
    def __init__(self, event_id: str = None, occurred_at: datetime = None):
        self.event_id = event_id or str(uuid4())
        self.occurred_at = occurred_at or datetime.now()
        self.event_type = self.__class__.__name__


class BaseEntity(ABC):
    """
    Abstract base entity implementing common functionality.
    Follows Single Responsibility Principle and provides event handling.
    """
    
    def __init__(self):
        self._domain_events: List[DomainEvent] = []
        self._is_dirty = False
    
    def add_domain_event(self, event: DomainEvent) -> None:
        """Add domain event following Observer pattern."""
        self._domain_events.append(event)
        self._is_dirty = True
    
    def clear_domain_events(self) -> None:
        """Clear all domain events."""
        self._domain_events.clear()
    
    def get_domain_events(self) -> List[DomainEvent]:
        """Get all domain events."""
        return self._domain_events.copy()
    
    @property
    def is_dirty(self) -> bool:
        """Check if entity has been modified."""
        return self._is_dirty
    
    def mark_clean(self) -> None:
        """Mark entity as clean (persisted)."""
        self._is_dirty = False
    
    def validate(self) -> List[str]:
        """
        Validate entity state and return list of validation errors.
        Override in derived classes for specific validation rules.
        """
        return []
    
    def is_valid(self) -> bool:
        """Check if entity is valid."""
        return len(self.validate()) == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary representation."""
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                elif hasattr(value, 'to_dict'):
                    result[key] = value.to_dict()
                else:
                    result[key] = value
        return result


class ValueObject(ABC):
    """
    Base class for value objects.
    Implements immutability and equality comparison.
    """
    
    def __init__(self, **kwargs):
        # Make value objects immutable
        for key, value in kwargs.items():
            object.__setattr__(self, f"_{key}", value)
    
    def __setattr__(self, name: str, value: Any) -> None:
        if hasattr(self, '_frozen'):
            raise AttributeError("Cannot modify immutable value object")
        super().__setattr__(name, value)
        if name != '_frozen':
            object.__setattr__(self, '_frozen', True)
    
    def __eq__(self, other: object) -> bool:
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self) -> int:
        return hash(tuple(sorted(self.__dict__.items())))


class AggregateRoot(BaseEntity):
    """
    Base class for aggregate roots in Domain-Driven Design.
    Manages consistency boundaries and domain events.
    """
    
    def __init__(self):
        super().__init__()
        self._version = 0
    
    @property
    def version(self) -> int:
        """Get aggregate version for optimistic concurrency control."""
        return self._version
    
    def increment_version(self) -> None:
        """Increment version for optimistic concurrency control."""
        self._version += 1
        self._is_dirty = True
