"""
Bill Domain Model
Defines the Bill entity for hospital billing management.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.domain.shared.base_entity import BaseEntity
from apps.domain.models.patient import <PERSON><PERSON><PERSON>han
from apps.domain.models.appointment import LichHen
from apps.domain.models.prescription import <PERSON><PERSON><PERSON><PERSON>
from decimal import Decimal


class HoaDon(BaseEntity):
    """
    Bill model for hospital management system.
    Represents bills for medical services and medications.
    """
    
    TRANG_THAI_CHOICES = [
        ('Draft', 'Bản nháp'),
        ('Issued', 'Đã phát hành'),
        ('Paid', 'Đã thanh toán'),
        ('Partially_Paid', '<PERSON>h toán một phần'),
        ('Overdue', 'Quá hạn'),
        ('Cancelled', 'Đã hủy'),
        ('Refunded', 'Đã hoàn tiền'),
    ]
    
    LOAI_THANH_TOAN_CHOICES = [
        ('<PERSON><PERSON>_Mat', 'Tiền mặt'),
        ('<PERSON>_Tin_Dung', 'Thẻ tín dụng'),
        ('<PERSON>ye<PERSON>_Khoan', '<PERSON><PERSON><PERSON><PERSON> khoản'),
        ('<PERSON><PERSON>_Hiem', '<PERSON><PERSON>o hiểm'),
        ('Tra_Gop', 'Trả góp'),
    ]
    
    # Primary Key
    HoaDonID = models.AutoField(primary_key=True)
    
    # Foreign Keys
    benh_nhan = models.ForeignKey(
        BenhNhan,
        on_delete=models.CASCADE,
        related_name='hoa_don',
        verbose_name='Bệnh nhân',
        help_text='Bệnh nhân được lập hóa đơn'
    )
    
    lich_hen = models.ForeignKey(
        LichHen,
        on_delete=models.SET_NULL,
        related_name='hoa_don',
        verbose_name='Lịch hẹn',
        blank=True,
        null=True,
        help_text='Lịch hẹn liên quan (nếu có)'
    )
    
    don_thuoc = models.ForeignKey(
        DonThuoc,
        on_delete=models.SET_NULL,
        related_name='hoa_don',
        verbose_name='Đơn thuốc',
        blank=True,
        null=True,
        help_text='Đơn thuốc liên quan (nếu có)'
    )
    
    # Bill Information
    ma_hoa_don = models.CharField(
        'Mã hóa đơn',
        max_length=30,
        unique=True,
        help_text='Mã định danh duy nhất của hóa đơn'
    )
    
    mo_ta = models.TextField(
        'Mô tả dịch vụ',
        help_text='Mô tả các dịch vụ y tế được cung cấp'
    )
    
    # Financial Information
    phi_kham = models.DecimalField(
        'Phí khám',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Chi phí khám bệnh (VND)'
    )
    
    phi_thuoc = models.DecimalField(
        'Phí thuốc',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Chi phí thuốc (VND)'
    )
    
    phi_dich_vu = models.DecimalField(
        'Phí dịch vụ',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Phí các dịch vụ khác (VND)'
    )
    
    tong_tien = models.DecimalField(
        'Tổng tiền',
        max_digits=12,
        decimal_places=0,
        help_text='Tổng số tiền phải thanh toán (VND)'
    )
    
    da_thanh_toan = models.DecimalField(
        'Đã thanh toán',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Số tiền đã thanh toán (VND)'
    )
    
    con_no = models.DecimalField(
        'Còn nợ',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Số tiền còn nợ (VND)'
    )
    
    # Payment Information
    phuong_thuc_thanh_toan = models.CharField(
        'Phương thức thanh toán',
        max_length=20,
        choices=LOAI_THANH_TOAN_CHOICES,
        blank=True,
        null=True,
        help_text='Phương thức thanh toán chính'
    )
    
    # Insurance Information  
    bao_hiem_chi_tra = models.DecimalField(
        'Bảo hiểm chi trả',
        max_digits=12,
        decimal_places=0,
        default=0,
        help_text='Số tiền bảo hiểm chi trả (VND)'
    )
    
    # Status and Tracking
    trang_thai = models.CharField(
        'Trạng thái',
        max_length=20,
        choices=TRANG_THAI_CHOICES,
        default='Draft',
        help_text='Trạng thái hóa đơn'
    )
    
    ghi_chu = models.TextField(
        'Ghi chú',
        blank=True,
        null=True,
        help_text='Ghi chú về hóa đơn'
    )
    
    # Timestamps
    ngay_tao = models.DateTimeField(
        'Ngày tạo',
        default=timezone.now,
        help_text='Ngày tạo hóa đơn'
    )
    
    ngay_dao_han = models.DateField(
        'Ngày đáo hạn',
        help_text='Ngày đáo hạn thanh toán'
    )
    
    ngay_thanh_toan = models.DateTimeField(
        'Ngày thanh toán',
        blank=True,
        null=True,
        help_text='Ngày thanh toán hoàn tất'
    )
    
    ngay_cap_nhat = models.DateTimeField(
        'Ngày cập nhật',
        auto_now=True,
        help_text='Ngày cập nhật lần cuối'
    )
    
    class Meta:
        db_table = 'HoaDon'
        verbose_name = 'Hóa đơn'
        verbose_name_plural = 'Hóa đơn'
        ordering = ['-ngay_tao']
        indexes = [
            models.Index(fields=['ma_hoa_don']),
            models.Index(fields=['benh_nhan', 'trang_thai']),
            models.Index(fields=['trang_thai']),
            models.Index(fields=['ngay_tao']),
            models.Index(fields=['ngay_dao_han']),
            models.Index(fields=['ngay_thanh_toan']),
        ]
    
    def __str__(self):
        return f"Hóa đơn {self.ma_hoa_don} - {self.benh_nhan.ho_ten}"
    
    def save(self, *args, **kwargs):
        """Override save to generate bill code and calculate amounts."""
        if not self.ma_hoa_don:
            self.ma_hoa_don = self.generate_bill_code()
        
        if not self.ngay_dao_han:
            from datetime import timedelta
            self.ngay_dao_han = (self.ngay_tao + timedelta(days=30)).date()
        
        # Calculate totals
        self.calculate_totals()
        
        self.full_clean()
        super().save(*args, **kwargs)
    
    def generate_bill_code(self) -> str:
        """Generate unique bill code."""
        from datetime import datetime
        
        today = datetime.now()
        date_str = today.strftime('%Y%m%d')
        prefix = f"HD{date_str}"
        
        existing_codes = HoaDon.objects.filter(
            ma_hoa_don__startswith=prefix
        ).values_list('ma_hoa_don', flat=True)
        
        max_seq = 0
        for code in existing_codes:
            try:
                seq = int(code[len(prefix):])
                max_seq = max(max_seq, seq)
            except (ValueError, IndexError):
                continue
        
        return f"{prefix}{max_seq + 1:06d}"
    
    def calculate_totals(self):
        """Calculate bill totals."""
        # Load prescription amount if linked
        if self.don_thuoc:
            self.phi_thuoc = self.don_thuoc.tong_tien
        
        # Calculate total
        self.tong_tien = self.phi_kham + self.phi_thuoc + self.phi_dich_vu
        
        # Calculate remaining balance
        self.con_no = self.tong_tien - self.da_thanh_toan - self.bao_hiem_chi_tra
        
        # Ensure non-negative balance
        if self.con_no < 0:
            self.con_no = 0
    
    # Business Methods
    def is_paid(self) -> bool:
        """Check if bill is fully paid."""
        return self.trang_thai == 'Paid' and self.con_no == 0
    
    def is_overdue(self) -> bool:
        """Check if bill is overdue."""
        from datetime import date
        return (not self.is_paid() and 
                self.ngay_dao_han < date.today() and
                self.trang_thai not in ['Cancelled', 'Refunded'])
    
    def can_be_paid(self) -> bool:
        """Check if bill can be paid."""
        return self.trang_thai in ['Issued', 'Partially_Paid', 'Overdue'] and self.con_no > 0
    
    def process_payment(self, amount: Decimal, payment_method: str, notes: str = None):
        """Process payment for bill."""
        if not self.can_be_paid():
            raise ValueError("Hóa đơn không thể thanh toán")
        
        if amount <= 0:
            raise ValueError("Số tiền thanh toán phải lớn hơn 0")
        
        if amount > self.con_no:
            raise ValueError("Số tiền thanh toán không được vượt quá số nợ")
        
        old_paid = self.da_thanh_toan
        self.da_thanh_toan += amount
        self.con_no = self.tong_tien - self.da_thanh_toan - self.bao_hiem_chi_tra
        
        # Update payment method if provided
        if payment_method:
            self.phuong_thuc_thanh_toan = payment_method
        
        # Update status
        if self.con_no == 0:
            self.trang_thai = 'Paid'
            self.ngay_thanh_toan = timezone.now()
        else:
            self.trang_thai = 'Partially_Paid'
        
        # Add notes
        if notes:
            payment_note = f"Thanh toán {amount:,.0f} VND - {notes}"
        else:
            payment_note = f"Thanh toán {amount:,.0f} VND"
        
        self.ghi_chu = f"{self.ghi_chu or ''}\n{payment_note}".strip()
        
        self.save(update_fields=[
            'da_thanh_toan', 'con_no', 'trang_thai', 'ngay_thanh_toan',
            'phuong_thuc_thanh_toan', 'ghi_chu', 'ngay_cap_nhat'
        ])
        
        # Raise domain event
        self.raise_payment_processed_event(amount, payment_method)
    
    def apply_insurance_coverage(self, coverage_amount: Decimal, notes: str = None):
        """Apply insurance coverage to bill."""
        if coverage_amount <= 0:
            raise ValueError("Số tiền bảo hiểm phải lớn hơn 0")
        
        if coverage_amount > self.tong_tien:
            raise ValueError("Số tiền bảo hiểm không được vượt quá tổng tiền")
        
        old_insurance = self.bao_hiem_chi_tra
        self.bao_hiem_chi_tra = coverage_amount
        self.con_no = self.tong_tien - self.da_thanh_toan - self.bao_hiem_chi_tra
        
        if self.con_no <= 0:
            self.con_no = 0
            if self.da_thanh_toan + self.bao_hiem_chi_tra >= self.tong_tien:
                self.trang_thai = 'Paid'
                self.ngay_thanh_toan = timezone.now()
        
        # Add notes
        insurance_note = f"Bảo hiểm chi trả {coverage_amount:,.0f} VND"
        if notes:
            insurance_note += f" - {notes}"
        
        self.ghi_chu = f"{self.ghi_chu or ''}\n{insurance_note}".strip()
        
        self.save(update_fields=[
            'bao_hiem_chi_tra', 'con_no', 'trang_thai', 'ngay_thanh_toan',
            'ghi_chu', 'ngay_cap_nhat'
        ])
        
        # Raise domain event
        self.raise_insurance_applied_event(coverage_amount)
    
    def issue_bill(self):
        """Issue the bill."""
        if self.trang_thai != 'Draft':
            raise ValueError("Chỉ có thể phát hành hóa đơn ở trạng thái bản nháp")
        
        self.trang_thai = 'Issued'
        self.save(update_fields=['trang_thai', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_bill_issued_event()
    
    def cancel_bill(self, reason: str):
        """Cancel the bill."""
        if self.trang_thai in ['Paid', 'Cancelled', 'Refunded']:
            raise ValueError("Không thể hủy hóa đơn đã thanh toán, đã hủy hoặc đã hoàn tiền")
        
        old_status = self.trang_thai
        self.trang_thai = 'Cancelled'
        
        cancel_note = f"Hủy hóa đơn: {reason}"
        self.ghi_chu = f"{self.ghi_chu or ''}\n{cancel_note}".strip()
        
        self.save(update_fields=['trang_thai', 'ghi_chu', 'ngay_cap_nhat'])
        
        # Raise domain event
        self.raise_bill_cancelled_event(reason, old_status)
    
    def process_refund(self, refund_amount: Decimal, reason: str):
        """Process refund for bill."""
        if not self.is_paid():
            raise ValueError("Chỉ có thể hoàn tiền cho hóa đơn đã thanh toán")
        
        if refund_amount > self.da_thanh_toan:
            raise ValueError("Số tiền hoàn không được vượt quá số đã thanh toán")
        
        self.da_thanh_toan -= refund_amount
        self.con_no = self.tong_tien - self.da_thanh_toan - self.bao_hiem_chi_tra
        self.trang_thai = 'Refunded'
        
        refund_note = f"Hoàn tiền {refund_amount:,.0f} VND - {reason}"
        self.ghi_chu = f"{self.ghi_chu or ''}\n{refund_note}".strip()
        
        self.save(update_fields=[
            'da_thanh_toan', 'con_no', 'trang_thai', 'ghi_chu', 'ngay_cap_nhat'
        ])
        
        # Raise domain event
        self.raise_refund_processed_event(refund_amount, reason)
    
    @property
    def payment_status_display(self) -> str:
        """Get payment status display."""
        if self.is_paid():
            return "Đã thanh toán đầy đủ"
        elif self.da_thanh_toan > 0:
            return f"Đã thanh toán {self.da_thanh_toan:,.0f}/{self.tong_tien:,.0f} VND"
        else:
            return "Chưa thanh toán"
    
    @property
    def bill_summary(self) -> dict:
        """Get bill summary."""
        return {
            'code': self.ma_hoa_don,
            'patient': self.benh_nhan.ho_ten,
            'status': self.get_trang_thai_display(),
            'total_amount': float(self.tong_tien),
            'paid_amount': float(self.da_thanh_toan),
            'insurance_coverage': float(self.bao_hiem_chi_tra),
            'remaining_balance': float(self.con_no),
            'payment_method': self.get_phuong_thuc_thanh_toan_display() if self.phuong_thuc_thanh_toan else None,
            'issue_date': self.ngay_tao,
            'due_date': self.ngay_dao_han,
            'payment_date': self.ngay_thanh_toan,
            'is_overdue': self.is_overdue()
        }
    
    # Domain Events
    def raise_bill_created_event(self):
        """Raise bill created domain event."""
        from apps.domain.events.domain_events import BillCreatedEvent
        event = BillCreatedEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            total_amount=float(self.tong_tien),
            due_date=self.ngay_dao_han,
            created_at=self.ngay_tao
        )
        self.add_domain_event(event)
    
    def raise_bill_issued_event(self):
        """Raise bill issued domain event."""
        from apps.domain.events.domain_events import BillIssuedEvent
        event = BillIssuedEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            total_amount=float(self.tong_tien),
            due_date=self.ngay_dao_han,
            issued_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_payment_processed_event(self, amount: Decimal, payment_method: str):
        """Raise payment processed domain event."""
        from apps.domain.events.domain_events import PaymentProcessedEvent
        event = PaymentProcessedEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            payment_amount=float(amount),
            payment_method=payment_method,
            total_paid=float(self.da_thanh_toan),
            remaining_balance=float(self.con_no),
            processed_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_insurance_applied_event(self, coverage_amount: Decimal):
        """Raise insurance applied domain event."""
        from apps.domain.events.domain_events import InsuranceAppliedEvent
        event = InsuranceAppliedEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            coverage_amount=float(coverage_amount),
            remaining_balance=float(self.con_no),
            applied_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_bill_cancelled_event(self, reason: str, old_status: str):
        """Raise bill cancelled domain event."""
        from apps.domain.events.domain_events import BillCancelledEvent
        event = BillCancelledEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            cancellation_reason=reason,
            old_status=old_status,
            cancelled_at=timezone.now()
        )
        self.add_domain_event(event)
    
    def raise_refund_processed_event(self, refund_amount: Decimal, reason: str):
        """Raise refund processed domain event."""
        from apps.domain.events.domain_events import RefundProcessedEvent
        event = RefundProcessedEvent(
            bill_id=self.HoaDonID,
            bill_code=self.ma_hoa_don,
            patient_id=self.benh_nhan.BenhNhanID,
            refund_amount=float(refund_amount),
            refund_reason=reason,
            processed_at=timezone.now()
        )
        self.add_domain_event(event)
